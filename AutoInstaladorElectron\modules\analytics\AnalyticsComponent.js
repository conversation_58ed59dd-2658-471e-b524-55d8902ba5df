class AnalyticsComponent {
    constructor() {
        this.apiUrl = 'http://localhost:5000/api';
        this.metrics = [];
        this.reports = [];
        this.dashboards = [];
        this.kpis = [];
        this.kpiValues = [];
        this.refreshInterval = null;
        this.currentDashboard = null;
    }

    async render() {
        return `
            <div class="analytics-module">
                <div class="module-header">
                    <h2>📊 Dashboard Analytics Avançado</h2>
                    <div class="header-actions">
                        <div class="analytics-status" id="analytics-status">
                            <span class="status-indicator" id="analytics-status-indicator">🟢</span>
                            <span id="analytics-status-text">Analytics Ativo</span>
                        </div>
                        <button class="btn btn-secondary" onclick="analyticsComponent.refreshData()">
                            <i class="icon">🔄</i> Atualizar
                        </button>
                        <button class="btn btn-info" onclick="analyticsComponent.showSettings()">
                            <i class="icon">⚙️</i> Configurações
                        </button>
                    </div>
                </div>

                <div class="analytics-dashboard">
                    <div class="dashboard-summary">
                        <div class="summary-card metrics-card">
                            <div class="card-icon">📈</div>
                            <div class="card-content">
                                <h3>Métricas</h3>
                                <div class="card-value" id="metrics-count">0</div>
                                <div class="card-subtitle">Últimas 24h</div>
                            </div>
                        </div>

                        <div class="summary-card reports-card">
                            <div class="card-icon">📄</div>
                            <div class="card-content">
                                <h3>Relatórios</h3>
                                <div class="card-value" id="reports-count">0</div>
                                <div class="card-subtitle">Disponíveis</div>
                            </div>
                        </div>

                        <div class="summary-card dashboards-card">
                            <div class="card-icon">📊</div>
                            <div class="card-content">
                                <h3>Dashboards</h3>
                                <div class="card-value" id="dashboards-count">0</div>
                                <div class="card-subtitle">Personalizados</div>
                            </div>
                        </div>

                        <div class="summary-card kpis-card">
                            <div class="card-icon">🎯</div>
                            <div class="card-content">
                                <h3>KPIs</h3>
                                <div class="card-value" id="kpis-count">0</div>
                                <div class="card-subtitle">Ativos</div>
                            </div>
                        </div>

                        <div class="summary-card performance-card">
                            <div class="card-icon">⚡</div>
                            <div class="card-content">
                                <h3>Performance</h3>
                                <div class="card-value" id="performance-score">95%</div>
                                <div class="card-subtitle">Score Geral</div>
                            </div>
                        </div>
                    </div>

                    <div class="analytics-tabs">
                        <div class="tab-buttons">
                            <button class="tab-btn active" onclick="analyticsComponent.switchTab('overview')">
                                📊 Visão Geral
                            </button>
                            <button class="tab-btn" onclick="analyticsComponent.switchTab('metrics')">
                                📈 Métricas
                            </button>
                            <button class="tab-btn" onclick="analyticsComponent.switchTab('reports')">
                                📄 Relatórios
                            </button>
                            <button class="tab-btn" onclick="analyticsComponent.switchTab('dashboards')">
                                📊 Dashboards
                            </button>
                            <button class="tab-btn" onclick="analyticsComponent.switchTab('kpis')">
                                🎯 KPIs
                            </button>
                            <button class="tab-btn" onclick="analyticsComponent.switchTab('trends')">
                                📈 Tendências
                            </button>
                        </div>

                        <div class="tab-content">
                            <div id="overview-tab" class="tab-pane active">
                                <div class="overview-grid">
                                    <div class="overview-section">
                                        <h3>📈 Métricas em Tempo Real</h3>
                                        <div id="realtime-metrics" class="metrics-grid">
                                            <div class="loading-placeholder">
                                                <div class="spinner"></div>
                                                <p>Carregando métricas...</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="overview-section">
                                        <h3>🎯 KPIs Principais</h3>
                                        <div id="main-kpis" class="kpis-grid">
                                            <div class="loading-placeholder">
                                                <div class="spinner"></div>
                                                <p>Carregando KPIs...</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="overview-section full-width">
                                        <h3>📊 Gráfico de Tendências</h3>
                                        <div id="trends-chart" class="chart-container">
                                            <canvas id="trendsCanvas" width="800" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="metrics-tab" class="tab-pane">
                                <div class="section-header">
                                    <h3>Métricas de Negócio</h3>
                                    <div class="section-actions">
                                        <select id="metrics-category" onchange="analyticsComponent.filterMetrics()">
                                            <option value="">Todas as Categorias</option>
                                            <option value="performance">Performance</option>
                                            <option value="usage">Uso</option>
                                            <option value="deployment">Deploy</option>
                                            <option value="cost">Custo</option>
                                        </select>
                                        <input type="date" id="metrics-start-date" onchange="analyticsComponent.filterMetrics()">
                                        <input type="date" id="metrics-end-date" onchange="analyticsComponent.filterMetrics()">
                                        <button class="btn btn-primary" onclick="analyticsComponent.exportMetrics()">
                                            📊 Exportar
                                        </button>
                                    </div>
                                </div>
                                <div id="metrics-list" class="analytics-list">
                                    <div class="loading-placeholder">
                                        <div class="spinner"></div>
                                        <p>Carregando métricas...</p>
                                    </div>
                                </div>
                            </div>

                            <div id="reports-tab" class="tab-pane">
                                <div class="section-header">
                                    <h3>Relatórios Personalizáveis</h3>
                                    <div class="section-actions">
                                        <select id="reports-status" onchange="analyticsComponent.filterReports()">
                                            <option value="">Todos os Status</option>
                                            <option value="Ready">Prontos</option>
                                            <option value="Generating">Gerando</option>
                                            <option value="Draft">Rascunho</option>
                                            <option value="Failed">Falhou</option>
                                        </select>
                                        <button class="btn btn-success" onclick="analyticsComponent.createReport()">
                                            📄 Novo Relatório
                                        </button>
                                    </div>
                                </div>
                                <div id="reports-list" class="analytics-list">
                                    <div class="loading-placeholder">
                                        <div class="spinner"></div>
                                        <p>Carregando relatórios...</p>
                                    </div>
                                </div>
                            </div>

                            <div id="dashboards-tab" class="tab-pane">
                                <div class="section-header">
                                    <h3>Dashboards Personalizados</h3>
                                    <div class="section-actions">
                                        <button class="btn btn-success" onclick="analyticsComponent.createDashboard()">
                                            📊 Novo Dashboard
                                        </button>
                                        <button class="btn btn-info" onclick="analyticsComponent.importDashboard()">
                                            📥 Importar
                                        </button>
                                    </div>
                                </div>
                                <div id="dashboards-list" class="dashboards-grid">
                                    <div class="loading-placeholder">
                                        <div class="spinner"></div>
                                        <p>Carregando dashboards...</p>
                                    </div>
                                </div>
                            </div>

                            <div id="kpis-tab" class="tab-pane">
                                <div class="section-header">
                                    <h3>Indicadores-Chave de Performance</h3>
                                    <div class="section-actions">
                                        <select id="kpis-category" onchange="analyticsComponent.filterKPIs()">
                                            <option value="">Todas as Categorias</option>
                                            <option value="Performance">Performance</option>
                                            <option value="Availability">Disponibilidade</option>
                                            <option value="Usage">Uso</option>
                                            <option value="Cost">Custo</option>
                                        </select>
                                        <button class="btn btn-primary" onclick="analyticsComponent.calculateKPIs()">
                                            🔄 Calcular KPIs
                                        </button>
                                        <button class="btn btn-success" onclick="analyticsComponent.createKPI()">
                                            🎯 Novo KPI
                                        </button>
                                    </div>
                                </div>
                                <div id="kpis-list" class="kpis-grid">
                                    <div class="loading-placeholder">
                                        <div class="spinner"></div>
                                        <p>Carregando KPIs...</p>
                                    </div>
                                </div>
                            </div>

                            <div id="trends-tab" class="tab-pane">
                                <div class="section-header">
                                    <h3>Análise de Tendências</h3>
                                    <div class="section-actions">
                                        <select id="trend-metric" onchange="analyticsComponent.updateTrendAnalysis()">
                                            <option value="">Selecionar Métrica</option>
                                            <option value="cpu_usage">CPU Usage</option>
                                            <option value="memory_usage">Memory Usage</option>
                                            <option value="response_time">Response Time</option>
                                        </select>
                                        <select id="trend-period" onchange="analyticsComponent.updateTrendAnalysis()">
                                            <option value="7">Últimos 7 dias</option>
                                            <option value="30">Últimos 30 dias</option>
                                            <option value="90">Últimos 90 dias</option>
                                        </select>
                                        <button class="btn btn-primary" onclick="analyticsComponent.analyzeTrend()">
                                            📈 Analisar
                                        </button>
                                        <button class="btn btn-info" onclick="analyticsComponent.comparePeriods()">
                                            📊 Comparar Períodos
                                        </button>
                                    </div>
                                </div>
                                <div id="trends-analysis" class="trends-container">
                                    <div class="trend-chart-container">
                                        <canvas id="trendAnalysisCanvas" width="800" height="400"></canvas>
                                    </div>
                                    <div id="trend-insights" class="trend-insights">
                                        <div class="loading-placeholder">
                                            <div class="spinner"></div>
                                            <p>Selecione uma métrica para análise...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async initialize() {
        console.log('📊 Inicializando módulo de Analytics...');

        // Carregar CSS
        this.loadCSS();

        // Criar interface se não existir
        this.createInterface();

        await this.loadDashboard();
        this.startAutoRefresh();
    }

    /**
     * Carrega CSS do componente
     */
    loadCSS() {
        const cssId = 'analytics-styles';
        if (!document.getElementById(cssId)) {
            const link = document.createElement('link');
            link.id = cssId;
            link.rel = 'stylesheet';
            link.href = 'modules/analytics/analytics-styles.css';
            document.head.appendChild(link);
        }
    }

    /**
     * Cria interface do componente
     */
    createInterface() {
        const container = document.getElementById('main-content');
        if (!container) return;

        // Remover módulo existente se houver
        const existingModule = document.getElementById('analytics-module');
        if (existingModule) {
            existingModule.remove();
        }

        // Criar novo módulo
        const moduleDiv = document.createElement('div');
        moduleDiv.id = 'analytics-module';
        moduleDiv.className = 'module-container';
        moduleDiv.style.display = 'none';

        this.render().then(html => {
            moduleDiv.innerHTML = html;
            container.appendChild(moduleDiv);
        });
    }

    /**
     * Mostra o módulo
     */
    show() {
        const module = document.getElementById('analytics-module');
        if (module) {
            // Ocultar outros módulos
            document.querySelectorAll('.module-container').forEach(m => m.style.display = 'none');
            module.style.display = 'block';
            this.loadDashboard();
        }
    }

    /**
     * Oculta o módulo
     */
    hide() {
        const module = document.getElementById('analytics-module');
        if (module) {
            module.style.display = 'none';
        }
    }

    async loadDashboard() {
        try {
            const response = await fetch(`${this.apiUrl}/analytics/dashboard`);
            if (!response.ok) throw new Error('Falha ao carregar dashboard');

            const dashboard = await response.json();
            this.updateDashboardSummary(dashboard.summary);
            
            // Carregar dados iniciais
            await Promise.all([
                this.loadMetrics(),
                this.loadReports(),
                this.loadDashboards(),
                this.loadKPIs(),
                this.loadKPIValues()
            ]);

        } catch (error) {
            console.error('Erro ao carregar dashboard de Analytics:', error);
            this.updateAnalyticsStatus('error', 'Erro no Analytics');
            showNotification('Erro ao carregar dashboard de Analytics: ' + error.message, 'error');
        }
    }

    updateDashboardSummary(summary) {
        document.getElementById('metrics-count').textContent = summary.totalMetrics || 0;
        document.getElementById('reports-count').textContent = summary.activeReports || 0;
        document.getElementById('dashboards-count').textContent = summary.customDashboards || 0;
        document.getElementById('kpis-count').textContent = summary.activeKPIs || 0;

        // Atualizar status do Analytics
        this.updateAnalyticsStatus('active', 'Analytics Ativo');
    }

    updateAnalyticsStatus(status, text) {
        const indicator = document.getElementById('analytics-status-indicator');
        const statusText = document.getElementById('analytics-status-text');
        
        if (indicator && statusText) {
            const statusMap = {
                'active': '🟢',
                'warning': '🟡',
                'error': '🔴',
                'inactive': '⚫'
            };
            
            indicator.textContent = statusMap[status] || '⚫';
            statusText.textContent = text;
        }
    }

    switchTab(tabName) {
        // Remover classe active de todos os botões e painéis
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
        
        // Adicionar classe active ao botão e painel selecionados
        event.target.classList.add('active');
        document.getElementById(`${tabName}-tab`).classList.add('active');

        // Carregar dados específicos da aba se necessário
        this.loadTabData(tabName);
    }

    async loadTabData(tabName) {
        switch (tabName) {
            case 'overview':
                await this.loadOverview();
                break;
            case 'metrics':
                await this.loadMetrics();
                break;
            case 'reports':
                await this.loadReports();
                break;
            case 'dashboards':
                await this.loadDashboards();
                break;
            case 'kpis':
                await this.loadKPIs();
                break;
            case 'trends':
                await this.loadTrends();
                break;
        }
    }

    async loadOverview() {
        try {
            // Carregar métricas em tempo real
            await this.loadRealtimeMetrics();
            
            // Carregar KPIs principais
            await this.loadMainKPIs();
            
            // Carregar gráfico de tendências
            await this.loadTrendsChart();
            
        } catch (error) {
            console.error('Erro ao carregar visão geral:', error);
        }
    }

    async loadRealtimeMetrics() {
        try {
            const endDate = new Date();
            const startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000); // 24h atrás

            const response = await fetch(
                `${this.apiUrl}/analytics/metrics?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}&limit=50`
            );
            
            if (!response.ok) throw new Error('Falha ao carregar métricas');

            const metrics = await response.json();
            this.renderRealtimeMetrics(metrics);
        } catch (error) {
            console.error('Erro ao carregar métricas em tempo real:', error);
            this.renderError('realtime-metrics', 'Erro ao carregar métricas');
        }
    }

    renderRealtimeMetrics(metrics) {
        const container = document.getElementById('realtime-metrics');
        if (!container) return;

        if (metrics.length === 0) {
            container.innerHTML = '<div class="empty-state">Nenhuma métrica disponível</div>';
            return;
        }

        // Agrupar métricas por categoria
        const groupedMetrics = metrics.reduce((acc, metric) => {
            if (!acc[metric.category]) acc[metric.category] = [];
            acc[metric.category].push(metric);
            return acc;
        }, {});

        container.innerHTML = Object.entries(groupedMetrics).map(([category, categoryMetrics]) => {
            const latestMetric = categoryMetrics[categoryMetrics.length - 1];
            return `
                <div class="metric-card ${category}">
                    <div class="metric-header">
                        <span class="metric-category">${this.getCategoryIcon(category)} ${category.toUpperCase()}</span>
                        <span class="metric-timestamp">${this.formatTime(latestMetric.timestamp)}</span>
                    </div>
                    <div class="metric-content">
                        <div class="metric-name">${latestMetric.metricName}</div>
                        <div class="metric-value">${latestMetric.value.toFixed(2)} ${latestMetric.unit}</div>
                        <div class="metric-trend ${this.calculateTrend(categoryMetrics)}">${this.getTrendIcon(this.calculateTrend(categoryMetrics))}</div>
                    </div>
                </div>
            `;
        }).join('');
    }

    async loadMainKPIs() {
        try {
            const response = await fetch(`${this.apiUrl}/analytics/kpis/values`);
            if (!response.ok) throw new Error('Falha ao carregar KPIs');

            const kpiValues = await response.json();
            this.renderMainKPIs(kpiValues.slice(0, 4)); // Top 4 KPIs
        } catch (error) {
            console.error('Erro ao carregar KPIs principais:', error);
            this.renderError('main-kpis', 'Erro ao carregar KPIs');
        }
    }

    renderMainKPIs(kpiValues) {
        const container = document.getElementById('main-kpis');
        if (!container) return;

        if (kpiValues.length === 0) {
            container.innerHTML = '<div class="empty-state">Nenhum KPI disponível</div>';
            return;
        }

        container.innerHTML = kpiValues.map(kpi => `
            <div class="kpi-card ${kpi.status.toLowerCase()}">
                <div class="kpi-header">
                    <span class="kpi-name">${kpi.kpiId}</span>
                    <span class="kpi-status ${kpi.status.toLowerCase()}">${this.getKPIStatusIcon(kpi.status)}</span>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">${kpi.value.toFixed(2)}</div>
                    <div class="kpi-change ${kpi.changePercent >= 0 ? 'positive' : 'negative'}">
                        ${kpi.changePercent ? (kpi.changePercent >= 0 ? '+' : '') + kpi.changePercent.toFixed(1) + '%' : 'N/A'}
                    </div>
                </div>
            </div>
        `).join('');
    }

    async loadTrendsChart() {
        try {
            // Implementar gráfico de tendências usando Chart.js
            const canvas = document.getElementById('trendsCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            
            // Dados simulados para demonstração
            const data = {
                labels: Array.from({length: 24}, (_, i) => `${i}:00`),
                datasets: [
                    {
                        label: 'CPU Usage',
                        data: Array.from({length: 24}, () => Math.random() * 100),
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'Memory Usage',
                        data: Array.from({length: 24}, () => Math.random() * 100),
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    }
                ]
            };

            if (window.trendsChart) {
                window.trendsChart.destroy();
            }

            window.trendsChart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Tendências das Últimas 24 Horas'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Erro ao carregar gráfico de tendências:', error);
        }
    }

    async loadMetrics() {
        try {
            const response = await fetch(`${this.apiUrl}/analytics/metrics?limit=100`);
            if (!response.ok) throw new Error('Falha ao carregar métricas');

            this.metrics = await response.json();
            this.renderMetrics();
        } catch (error) {
            console.error('Erro ao carregar métricas:', error);
            this.renderError('metrics-list', 'Erro ao carregar métricas');
        }
    }

    renderMetrics() {
        const container = document.getElementById('metrics-list');
        if (!container) return;

        if (this.metrics.length === 0) {
            container.innerHTML = '<div class="empty-state">Nenhuma métrica disponível</div>';
            return;
        }

        container.innerHTML = this.metrics.map(metric => `
            <div class="analytics-item metric-item">
                <div class="item-header">
                    <div class="item-title">
                        <span class="metric-category">${this.getCategoryIcon(metric.category)} ${metric.category}</span>
                        <span class="metric-name">${metric.metricName}</span>
                    </div>
                    <div class="item-meta">
                        <span class="metric-type">${metric.type}</span>
                        <span class="timestamp">${this.formatTime(metric.timestamp)}</span>
                    </div>
                </div>
                <div class="item-content">
                    <div class="metric-details">
                        <div class="metric-value-display">
                            <span class="value">${metric.value.toFixed(2)}</span>
                            <span class="unit">${metric.unit}</span>
                        </div>
                        <div class="metric-source">Fonte: ${metric.source}</div>
                        ${Object.keys(metric.dimensions).length > 0 ? `
                            <div class="metric-dimensions">
                                <strong>Dimensões:</strong>
                                ${Object.entries(metric.dimensions).map(([key, value]) => 
                                    `<span class="dimension">${key}: ${value}</span>`
                                ).join(', ')}
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `).join('');
    }

    async loadReports() {
        try {
            const response = await fetch(`${this.apiUrl}/analytics/reports`);
            if (!response.ok) throw new Error('Falha ao carregar relatórios');

            this.reports = await response.json();
            this.renderReports();
        } catch (error) {
            console.error('Erro ao carregar relatórios:', error);
            this.renderError('reports-list', 'Erro ao carregar relatórios');
        }
    }

    renderReports() {
        const container = document.getElementById('reports-list');
        if (!container) return;

        if (this.reports.length === 0) {
            container.innerHTML = '<div class="empty-state">Nenhum relatório disponível</div>';
            return;
        }

        container.innerHTML = this.reports.map(report => `
            <div class="analytics-item report-item">
                <div class="item-header">
                    <div class="item-title">
                        <span class="report-type">${this.getReportIcon(report.type)} ${report.name}</span>
                        <span class="status-badge ${report.status.toLowerCase()}">${report.status}</span>
                    </div>
                    <div class="item-meta">
                        <span class="report-format">${report.configuration.format}</span>
                        <span class="timestamp">${this.formatTime(report.createdAt)}</span>
                    </div>
                </div>
                <div class="item-content">
                    <div class="report-description">${report.description}</div>
                    <div class="report-details">
                        <div class="detail-item">
                            <span class="label">Período:</span>
                            <span class="value">${this.formatDate(report.configuration.startDate)} - ${this.formatDate(report.configuration.endDate)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">Métricas:</span>
                            <span class="value">${report.configuration.metrics.length} métricas</span>
                        </div>
                        ${report.fileSizeBytes ? `
                            <div class="detail-item">
                                <span class="label">Tamanho:</span>
                                <span class="value">${this.formatFileSize(report.fileSizeBytes)}</span>
                            </div>
                        ` : ''}
                    </div>
                    <div class="item-actions">
                        ${report.status === 'Draft' ? `
                            <button class="btn btn-sm btn-primary" onclick="analyticsComponent.generateReport('${report.id}')">
                                🔄 Gerar
                            </button>
                        ` : ''}
                        ${report.status === 'Ready' ? `
                            <button class="btn btn-sm btn-success" onclick="analyticsComponent.downloadReport('${report.id}')">
                                📥 Download
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-secondary" onclick="analyticsComponent.viewReport('${report.id}')">
                            👁️ Visualizar
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="analyticsComponent.deleteReport('${report.id}')">
                            🗑️ Excluir
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    async loadDashboards() {
        try {
            const response = await fetch(`${this.apiUrl}/analytics/dashboards`);
            if (!response.ok) throw new Error('Falha ao carregar dashboards');

            this.dashboards = await response.json();
            this.renderDashboards();
        } catch (error) {
            console.error('Erro ao carregar dashboards:', error);
            this.renderError('dashboards-list', 'Erro ao carregar dashboards');
        }
    }

    renderDashboards() {
        const container = document.getElementById('dashboards-list');
        if (!container) return;

        if (this.dashboards.length === 0) {
            container.innerHTML = '<div class="empty-state">Nenhum dashboard disponível</div>';
            return;
        }

        container.innerHTML = this.dashboards.map(dashboard => `
            <div class="dashboard-card" onclick="analyticsComponent.openDashboard('${dashboard.id}')">
                <div class="dashboard-header">
                    <h4>${dashboard.name}</h4>
                    <div class="dashboard-meta">
                        <span class="created-by">Por: ${dashboard.createdBy}</span>
                        <span class="updated-at">${this.formatTime(dashboard.updatedAt)}</span>
                    </div>
                </div>
                <div class="dashboard-content">
                    <p class="dashboard-description">${dashboard.description}</p>
                    <div class="dashboard-stats">
                        <span class="stat">📊 ${dashboard.widgets.length} widgets</span>
                        <span class="stat">🎨 ${dashboard.layout.theme}</span>
                        ${dashboard.isPublic ? '<span class="stat public">🌐 Público</span>' : '<span class="stat private">🔒 Privado</span>'}
                    </div>
                </div>
                <div class="dashboard-actions">
                    <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); analyticsComponent.editDashboard('${dashboard.id}')">
                        ✏️ Editar
                    </button>
                    <button class="btn btn-sm btn-info" onclick="event.stopPropagation(); analyticsComponent.duplicateDashboard('${dashboard.id}')">
                        📋 Duplicar
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="event.stopPropagation(); analyticsComponent.exportDashboard('${dashboard.id}')">
                        📤 Exportar
                    </button>
                </div>
            </div>
        `).join('');
    }

    async loadKPIs() {
        try {
            const response = await fetch(`${this.apiUrl}/analytics/kpis`);
            if (!response.ok) throw new Error('Falha ao carregar KPIs');

            this.kpis = await response.json();
            this.renderKPIs();
        } catch (error) {
            console.error('Erro ao carregar KPIs:', error);
            this.renderError('kpis-list', 'Erro ao carregar KPIs');
        }
    }

    renderKPIs() {
        const container = document.getElementById('kpis-list');
        if (!container) return;

        if (this.kpis.length === 0) {
            container.innerHTML = '<div class="empty-state">Nenhum KPI definido</div>';
            return;
        }

        container.innerHTML = this.kpis.map(kpi => `
            <div class="kpi-definition-card">
                <div class="kpi-header">
                    <h4>${kpi.name}</h4>
                    <div class="kpi-meta">
                        <span class="kpi-category">${this.getKPITypeIcon(kpi.type)} ${kpi.category}</span>
                        <span class="kpi-status ${kpi.isActive ? 'active' : 'inactive'}">
                            ${kpi.isActive ? '🟢 Ativo' : '⚫ Inativo'}
                        </span>
                    </div>
                </div>
                <div class="kpi-content">
                    <p class="kpi-description">${kpi.description}</p>
                    <div class="kpi-details">
                        <div class="detail-item">
                            <span class="label">Tipo:</span>
                            <span class="value">${kpi.type}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">Unidade:</span>
                            <span class="value">${kpi.unit}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">Meta:</span>
                            <span class="value">${kpi.target.targetValue || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">Direção:</span>
                            <span class="value">${kpi.target.direction}</span>
                        </div>
                    </div>
                    ${kpi.target.thresholds.length > 0 ? `
                        <div class="kpi-thresholds">
                            <strong>Limites:</strong>
                            ${kpi.target.thresholds.map(threshold => `
                                <span class="threshold" style="color: ${threshold.color}">
                                    ${threshold.name}: ${threshold.operator} ${threshold.value}
                                </span>
                            `).join(', ')}
                        </div>
                    ` : ''}
                </div>
                <div class="kpi-actions">
                    <button class="btn btn-sm btn-primary" onclick="analyticsComponent.editKPI('${kpi.id}')">
                        ✏️ Editar
                    </button>
                    <button class="btn btn-sm btn-info" onclick="analyticsComponent.viewKPIHistory('${kpi.id}')">
                        📈 Histórico
                    </button>
                    <button class="btn btn-sm btn-${kpi.isActive ? 'warning' : 'success'}" onclick="analyticsComponent.toggleKPI('${kpi.id}')">
                        ${kpi.isActive ? '⏸️ Desativar' : '▶️ Ativar'}
                    </button>
                </div>
            </div>
        `).join('');
    }

    async loadKPIValues() {
        try {
            const response = await fetch(`${this.apiUrl}/analytics/kpis/values`);
            if (!response.ok) throw new Error('Falha ao carregar valores de KPIs');

            this.kpiValues = await response.json();
        } catch (error) {
            console.error('Erro ao carregar valores de KPIs:', error);
        }
    }

    async loadTrends() {
        // Implementar carregamento de análise de tendências
        console.log('Carregando análise de tendências...');
    }

    // Métodos de ação
    async generateReport(reportId) {
        try {
            showNotification('Gerando relatório...', 'info');
            
            const response = await fetch(`${this.apiUrl}/analytics/reports/${reportId}/generate`, {
                method: 'POST'
            });

            if (!response.ok) throw new Error('Falha ao gerar relatório');

            await this.loadReports();
            showNotification('Relatório gerado com sucesso!', 'success');
        } catch (error) {
            console.error('Erro ao gerar relatório:', error);
            showNotification('Erro ao gerar relatório: ' + error.message, 'error');
        }
    }

    async calculateKPIs() {
        try {
            showNotification('Calculando KPIs...', 'info');
            
            const response = await fetch(`${this.apiUrl}/analytics/kpis/calculate`, {
                method: 'POST'
            });

            if (!response.ok) throw new Error('Falha ao calcular KPIs');

            await this.loadKPIValues();
            await this.loadMainKPIs();
            showNotification('KPIs calculados com sucesso!', 'success');
        } catch (error) {
            console.error('Erro ao calcular KPIs:', error);
            showNotification('Erro ao calcular KPIs: ' + error.message, 'error');
        }
    }

    // Métodos utilitários
    getCategoryIcon(category) {
        const icons = {
            performance: '⚡',
            usage: '📊',
            deployment: '🚀',
            cost: '💰'
        };
        return icons[category] || '📈';
    }

    getReportIcon(type) {
        const icons = {
            Performance: '⚡',
            Usage: '📊',
            Deployment: '🚀',
            Cost: '💰',
            Security: '🔒',
            Custom: '🎯',
            Executive: '👔',
            Technical: '🔧'
        };
        return icons[type] || '📄';
    }

    getKPITypeIcon(type) {
        const icons = {
            Performance: '⚡',
            Availability: '🟢',
            Usage: '📊',
            Cost: '💰',
            Quality: '⭐',
            Efficiency: '🎯',
            Custom: '🔧'
        };
        return icons[type] || '🎯';
    }

    getKPIStatusIcon(status) {
        const icons = {
            Excellent: '🟢',
            Good: '🔵',
            Warning: '🟡',
            Critical: '🔴',
            Unknown: '⚫'
        };
        return icons[status] || '⚫';
    }

    getTrendIcon(trend) {
        const icons = {
            up: '📈',
            down: '📉',
            stable: '➡️'
        };
        return icons[trend] || '➡️';
    }

    calculateTrend(metrics) {
        if (metrics.length < 2) return 'stable';
        
        const recent = metrics.slice(-5).map(m => m.value);
        const older = metrics.slice(-10, -5).map(m => m.value);
        
        if (recent.length === 0 || older.length === 0) return 'stable';
        
        const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
        const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;
        
        const change = ((recentAvg - olderAvg) / olderAvg) * 100;
        
        return change > 5 ? 'up' : change < -5 ? 'down' : 'stable';
    }

    formatTime(dateString) {
        return new Date(dateString).toLocaleString('pt-BR');
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('pt-BR');
    }

    formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    renderError(containerId, message) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `<div class="error-state">❌ ${message}</div>`;
        }
    }

    // Métodos de filtro
    filterMetrics() {
        this.renderMetrics();
    }

    filterReports() {
        this.renderReports();
    }

    filterKPIs() {
        this.renderKPIs();
    }

    // Métodos de ação (placeholders)
    createReport() {
        showNotification('Funcionalidade de criação de relatórios em desenvolvimento', 'info');
    }

    createDashboard() {
        showNotification('Funcionalidade de criação de dashboards em desenvolvimento', 'info');
    }

    createKPI() {
        showNotification('Funcionalidade de criação de KPIs em desenvolvimento', 'info');
    }

    exportMetrics() {
        showNotification('Exportação de métricas em desenvolvimento', 'info');
    }

    analyzeTrend() {
        showNotification('Análise de tendências em desenvolvimento', 'info');
    }

    comparePeriods() {
        showNotification('Comparação de períodos em desenvolvimento', 'info');
    }

    async refreshData() {
        await this.loadDashboard();
        showNotification('Dados de Analytics atualizados!', 'success');
    }

    showSettings() {
        showNotification('Configurações de Analytics em desenvolvimento', 'info');
    }

    startAutoRefresh() {
        // Atualizar dados a cada 5 minutos
        this.refreshInterval = setInterval(() => {
            this.loadDashboard();
        }, 5 * 60 * 1000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // Métodos adicionais (continuação dos placeholders)
    downloadReport(reportId) {
        showNotification('Download de relatório em desenvolvimento', 'info');
    }

    viewReport(reportId) {
        showNotification('Visualização de relatório em desenvolvimento', 'info');
    }
    deleteReport(reportId) {
        if (confirm('Tem certeza que deseja excluir este relatório?')) {
            showNotification('Exclusão de relatório em desenvolvimento', 'info');
        }
    }
    openDashboard(dashboardId) {
        showNotification('Abertura de dashboard em desenvolvimento', 'info');
    }

    editDashboard(dashboardId) {
        showNotification('Edição de dashboard em desenvolvimento', 'info');
    }

    duplicateDashboard(dashboardId) {
        showNotification('Duplicação de dashboard em desenvolvimento', 'info');
    }

    exportDashboard(dashboardId) {
        showNotification('Exportação de dashboard em desenvolvimento', 'info');
    }

    importDashboard() {
        showNotification('Importação de dashboard em desenvolvimento', 'info');
    }

    editKPI(kpiId) {
        showNotification('Edição de KPI em desenvolvimento', 'info');
    }

    viewKPIHistory(kpiId) {
        showNotification('Histórico de KPI em desenvolvimento', 'info');
    }

    toggleKPI(kpiId) {
        showNotification('Alteração de status de KPI em desenvolvimento', 'info');
    }

    updateTrendAnalysis() {
        showNotification('Atualização de análise de tendências em desenvolvimento', 'info');
    }
}

// Instância global
const analyticsComponent = new AnalyticsComponent();
