// Script para limpar cache e forçar reload completo

console.log('🧹 LIMPANDO CACHE E FORÇANDO RELOAD');
console.log('==================================');

function clearAllCache() {
    try {
        // Limpar localStorage
        if (typeof localStorage !== 'undefined') {
            localStorage.clear();
            console.log('✅ localStorage limpo');
        }
        
        // Limpar sessionStorage
        if (typeof sessionStorage !== 'undefined') {
            sessionStorage.clear();
            console.log('✅ sessionStorage limpo');
        }
        
        // Limpar cache de módulos (se existir)
        if (window.moduleManager && window.moduleManager.modules) {
            window.moduleManager.modules.clear();
            console.log('✅ Cache de módulos limpo');
        }
        
        // Forçar reload da página
        console.log('🔄 Forçando reload completo...');
        location.reload(true);
        
    } catch (error) {
        console.error('❌ Erro ao limpar cache:', error);
    }
}

function hardRefresh() {
    console.log('💪 HARD REFRESH - Limpando tudo');
    
    // Limpar todos os caches possíveis
    clearAllCache();
    
    // Se não funcionar, tentar métodos alternativos
    setTimeout(() => {
        if (window.location) {
            window.location.href = window.location.href;
        }
    }, 100);
}

function testComponentDirectly() {
    console.log('🧪 TESTANDO COMPONENTE DIRETAMENTE');
    console.log('==================================');
    
    // Verificar se o componente existe
    if (typeof intelligentAgentComponent !== 'undefined') {
        console.log('✅ intelligentAgentComponent encontrado');
        
        // Testar renderização direta
        try {
            intelligentAgentComponent.renderSimpleTestPage();
            console.log('✅ Renderização direta executada');
        } catch (error) {
            console.error('❌ Erro na renderização direta:', error);
        }
    } else {
        console.log('❌ intelligentAgentComponent NÃO encontrado');
        
        // Listar componentes disponíveis
        const globals = Object.keys(window);
        const components = globals.filter(key => key.includes('Component'));
        console.log('🔍 Componentes disponíveis:', components);
    }
}

function debugModuleManager() {
    console.log('🔍 DEBUG DO MODULEMANAGER');
    console.log('========================');
    
    if (window.moduleManager) {
        console.log('✅ ModuleManager encontrado');
        console.log('📦 Módulos registrados:', window.moduleManager.modules?.size || 0);
        
        // Verificar módulo intelligent-agent
        const module = window.moduleManager.modules?.get('intelligent-agent');
        if (module) {
            console.log('✅ Módulo intelligent-agent registrado');
            console.log('📄 Scripts:', module.scripts);
            console.log('🎯 Componente:', module.component);
        } else {
            console.log('❌ Módulo intelligent-agent NÃO registrado');
        }
    } else {
        console.log('❌ ModuleManager NÃO encontrado');
    }
}

// Disponibilizar funções globalmente
window.clearAllCache = clearAllCache;
window.hardRefresh = hardRefresh;
window.testComponentDirectly = testComponentDirectly;
window.debugModuleManager = debugModuleManager;

console.log('🛠️ FUNÇÕES DE LIMPEZA DISPONÍVEIS:');
console.log('==================================');
console.log('  - clearAllCache()        // Limpa todos os caches');
console.log('  - hardRefresh()          // Hard refresh completo');
console.log('  - testComponentDirectly() // Testa componente diretamente');
console.log('  - debugModuleManager()   // Debug do ModuleManager');
console.log('');
console.log('💡 EXECUTE: hardRefresh() para limpar cache e recarregar');
console.log('💡 OU: testComponentDirectly() para testar sem recarregar');
