// Script para verificar consistência de nomenclatura dos componentes habilitados

console.log('🔍 VERIFICAÇÃO DE CONSISTÊNCIA DE NOMENCLATURA');
console.log('============================================\n');

// Componentes habilitados na estratégia gradual
const enabledComponents = [
    {
        moduleId: 'dashboard',
        expectedGlobal: 'dashboardComponent',
        file: 'modules/dashboard/DashboardComponent.js'
    },
    {
        moduleId: 'applications', 
        expectedGlobal: 'applicationsComponent',
        file: 'modules/applications/ApplicationsComponent.js'
    },
    {
        moduleId: 'docker',
        expectedGlobal: 'dockerComponent', 
        file: 'modules/docker/DockerComponent.js'
    },
    {
        moduleId: 'templates',
        expectedGlobal: 'templatesComponent',
        file: 'modules/templates/TemplatesComponent.js'
    },
    {
        moduleId: 'intelligent-agent',
        expectedGlobal: 'intelligentAgentComponent',
        file: 'modules/intelligent-agent/IntelligentAgentComponent.js'
    }
];

function checkNamingConsistency() {
    console.log('📊 VERIFICANDO NOMENCLATURA DOS COMPONENTES HABILITADOS');
    console.log('======================================================\n');
    
    let consistentComponents = 0;
    let inconsistentComponents = 0;
    const issues = [];
    
    enabledComponents.forEach((comp, index) => {
        console.log(`${index + 1}. Verificando ${comp.moduleId}:`);
        console.log(`   📁 Arquivo: ${comp.file}`);
        console.log(`   🎯 Global esperado: ${comp.expectedGlobal}`);
        
        // Verificar se o componente existe globalmente
        const globalComponent = window[comp.expectedGlobal];
        
        if (globalComponent) {
            console.log(`   ✅ Componente encontrado globalmente`);
            
            // Verificar se tem método initialize
            if (typeof globalComponent.initialize === 'function') {
                console.log(`   ✅ Método initialize() disponível`);
                consistentComponents++;
            } else {
                console.log(`   ❌ Método initialize() NÃO encontrado`);
                issues.push({
                    component: comp.moduleId,
                    issue: 'Método initialize() não encontrado',
                    global: comp.expectedGlobal
                });
                inconsistentComponents++;
            }
        } else {
            console.log(`   ❌ Componente NÃO encontrado globalmente`);
            
            // Verificar se existe com nome diferente
            const allGlobals = Object.keys(window);
            const possibleMatches = allGlobals.filter(key => 
                key.toLowerCase().includes(comp.moduleId.replace('-', '').toLowerCase()) ||
                key.toLowerCase().includes(comp.expectedGlobal.toLowerCase())
            );
            
            if (possibleMatches.length > 0) {
                console.log(`   🔍 Possíveis correspondências encontradas: ${possibleMatches.join(', ')}`);
                issues.push({
                    component: comp.moduleId,
                    issue: 'Nome inconsistente',
                    expected: comp.expectedGlobal,
                    found: possibleMatches
                });
            } else {
                console.log(`   ❌ Nenhuma correspondência encontrada`);
                issues.push({
                    component: comp.moduleId,
                    issue: 'Componente não carregado',
                    expected: comp.expectedGlobal
                });
            }
            
            inconsistentComponents++;
        }
        
        console.log('');
    });
    
    console.log('📊 RESUMO DA VERIFICAÇÃO:');
    console.log('========================');
    console.log(`✅ Componentes consistentes: ${consistentComponents}/5`);
    console.log(`❌ Componentes inconsistentes: ${inconsistentComponents}/5`);
    
    if (issues.length > 0) {
        console.log('\n🚨 PROBLEMAS ENCONTRADOS:');
        console.log('=========================');
        
        issues.forEach((issue, index) => {
            console.log(`${index + 1}. ${issue.component}:`);
            console.log(`   🔴 Problema: ${issue.issue}`);
            if (issue.expected) console.log(`   🎯 Esperado: ${issue.expected}`);
            if (issue.found) console.log(`   🔍 Encontrado: ${issue.found.join(', ')}`);
            console.log('');
        });
    }
    
    return {
        consistent: consistentComponents,
        inconsistent: inconsistentComponents,
        issues: issues,
        success: consistentComponents === 5
    };
}

function checkModuleManagerConfig() {
    console.log('⚙️ VERIFICANDO CONFIGURAÇÃO DO MODULEMANAGER');
    console.log('===========================================\n');
    
    // Verificar se o ModuleManager existe
    if (typeof window.moduleManager === 'undefined') {
        console.log('❌ ModuleManager não encontrado globalmente');
        return;
    }
    
    const moduleManager = window.moduleManager;
    
    // Verificar módulos registrados
    if (moduleManager.modules) {
        console.log(`📦 Módulos registrados no ModuleManager: ${moduleManager.modules.size}`);
        
        enabledComponents.forEach(comp => {
            const module = moduleManager.modules.get(comp.moduleId);
            if (module) {
                console.log(`✅ ${comp.moduleId}: Registrado`);
                console.log(`   🎯 Componente esperado: ${module.component}`);
                console.log(`   📄 Scripts: ${module.scripts ? module.scripts.join(', ') : 'Nenhum'}`);
            } else {
                console.log(`❌ ${comp.moduleId}: NÃO registrado`);
            }
        });
    } else {
        console.log('❌ ModuleManager.modules não encontrado');
    }
}

function suggestFixes() {
    console.log('\n🔧 SUGESTÕES DE CORREÇÃO:');
    console.log('========================');
    console.log('1. Verificar se todos os scripts estão sendo carregados');
    console.log('2. Verificar se as instanciações globais estão corretas');
    console.log('3. Verificar se os nomes dos componentes no ModuleManager estão corretos');
    console.log('4. Verificar se há erros de sintaxe impedindo o carregamento');
    console.log('\n💡 Execute as funções de diagnóstico:');
    console.log('   - checkNamingConsistency()');
    console.log('   - checkModuleManagerConfig()');
}

// Disponibilizar funções globalmente
window.checkNamingConsistency = checkNamingConsistency;
window.checkModuleManagerConfig = checkModuleManagerConfig;
window.suggestFixes = suggestFixes;

console.log('🧪 FUNÇÕES DE VERIFICAÇÃO DISPONÍVEIS:');
console.log('=====================================');
console.log('  - checkNamingConsistency()    // Verifica nomenclatura dos 5 componentes');
console.log('  - checkModuleManagerConfig()  // Verifica configuração do ModuleManager');
console.log('  - suggestFixes()              // Mostra sugestões de correção');
console.log('\n💡 EXECUTE: checkNamingConsistency() para verificar os componentes');
