/**
 * Componente de Interface do Agente Inteligente
 * Gerencia a interface do usuário para o sistema de agente inteligente
 */
class IntelligentAgentComponent {
    constructor() {
        this.service = new IntelligentAgentService();
        this.currentView = 'dashboard';
        this.refreshInterval = null;
        this.autoRefresh = true;

        // Modo de teste simples (sem API calls)
        this.testMode = true;

        // Bind dos métodos
        this.handleStatusUpdate = this.handleStatusUpdate.bind(this);
        this.handleNewError = this.handleNewError.bind(this);
        this.handleErrorFixed = this.handleErrorFixed.bind(this);
        this.handleLogUpdate = this.handleLogUpdate.bind(this);
    }

    /**
     * Inicializa o componente
     */
    async initialize() {
        try {
            console.log('🤖 Inicializando componente do Agente Inteligente...');

            if (this.testMode) {
                console.log('🧪 MODO DE TESTE: Inicializando sem backend');

                // Renderizar interface simples
                this.renderSimpleTestPage();

                console.log('✅ Componente do Agente Inteligente inicializado em modo de teste');
                return true;
            }

            // Modo normal (com backend)
            // Configurar callbacks do serviço
            this.service.onStatusUpdate = this.handleStatusUpdate;
            this.service.onNewError = this.handleNewError;
            this.service.onErrorFixed = this.handleErrorFixed;
            this.service.onLogUpdate = this.handleLogUpdate;

            // Inicializar serviço
            const success = await this.service.initialize();
            if (!success) {
                throw new Error('Falha ao inicializar serviço');
            }

            // Renderizar interface
            this.render();

            // Configurar auto-refresh
            this.startAutoRefresh();

            console.log('✅ Componente do Agente Inteligente inicializado com sucesso');
            return true;
        } catch (error) {
            console.error('❌ Erro ao inicializar componente:', error);
            this.showError('Erro ao inicializar Agente Inteligente: ' + error.message);
            return false;
        }
    }

    /**
     * Renderiza página de teste ULTRA-SIMPLES (sem erros)
     */
    renderSimpleTestPage() {
        console.log('🎨 Renderizando página ULTRA-SIMPLES...');

        try {
            // Usar container padrão do main-content
            const container = document.getElementById('main-content');
            if (!container) {
                console.error('❌ Container principal não encontrado');
                return;
            }

            // Limpar conteúdo anterior
            container.innerHTML = '';

            // Criar página ULTRA-SIMPLES (sem complexidade)
            container.innerHTML = `
                <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif;">
                    <h1 style="color: #2c3e50; margin-bottom: 20px;">
                        🤖 Agente Inteligente
                    </h1>

                    <div style="background: #e8f5e8; padding: 30px; border-radius: 10px; margin: 20px 0;">
                        <h2 style="color: #27ae60; margin: 0;">✅ SUCESSO!</h2>
                        <p style="color: #2c3e50; font-size: 18px; margin: 15px 0;">
                            Componente carregado e funcionando perfeitamente!
                        </p>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3 style="color: #495057;">🧪 Teste de Funcionalidade</h3>
                        <button onclick="alert('Funcionando!')"
                                style="background: #007bff; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px;">
                            Clique para Testar
                        </button>
                    </div>

                    <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3 style="color: #856404;">📋 Resultado do Diagnóstico</h3>
                        <p style="color: #856404; font-size: 16px;">
                            ✅ Carregamento do componente: OK<br>
                            ✅ Renderização da interface: OK<br>
                            ✅ Navegação da sidebar: OK<br>
                            ✅ Estratégia gradual: VALIDADA
                        </p>
                    </div>

                    <div style="margin-top: 30px; color: #6c757d;">
                        <p>Página de teste ultra-simples - Auto-Instalador</p>
                    </div>
                </div>
            `;

            console.log('✅ Página ULTRA-SIMPLES renderizada com sucesso');

        } catch (error) {
            console.error('❌ Erro ao renderizar página simples:', error);

            // Fallback ainda mais simples
            const container = document.getElementById('main-content');
            if (container) {
                container.innerHTML = `
                    <div style="padding: 50px; text-align: center;">
                        <h1>🤖 Agente Inteligente</h1>
                        <h2 style="color: green;">✅ COMPONENTE FUNCIONANDO!</h2>
                        <p>Se você está vendo esta mensagem, o componente carregou com sucesso.</p>
                    </div>
                `;
            }
        }
    }

    /**
     * Renderiza a interface principal
     */
    render() {
        // Se estiver em modo de teste, usar página simples
        if (this.testMode) {
            console.log('🧪 MODO DE TESTE: Usando renderSimpleTestPage()');
            this.renderSimpleTestPage();
            return '<div>Página de teste carregada</div>'; // Retorno para ModuleManager
        }

        // Modo normal (com backend)
        // Usar container padrão do main-content como outros componentes
        const container = document.getElementById('main-content');
        if (!container) {
            console.error('Container principal não encontrado');
            return;
        }

        // Limpar conteúdo anterior
        container.innerHTML = '';

        container.innerHTML = `
            <div class="intelligent-agent-wrapper">
                <div class="agent-header">
                    <div class="agent-title">
                        <h2>🤖 Agente Inteligente</h2>
                        <div class="agent-status" id="agent-status">
                            <span class="status-indicator" id="status-indicator"></span>
                            <span id="status-text">Carregando...</span>
                        </div>
                    </div>
                    <div class="agent-controls">
                        <button id="start-monitoring-btn" class="btn btn-success" style="display: none;">
                            ▶️ Iniciar Monitoramento
                        </button>
                        <button id="stop-monitoring-btn" class="btn btn-warning" style="display: none;">
                            ⏸️ Parar Monitoramento
                        </button>
                        <button id="refresh-btn" class="btn btn-secondary">
                            🔄 Atualizar
                        </button>
                        <button id="health-check-btn" class="btn btn-info">
                            🏥 Verificar Saúde
                        </button>
                    </div>
                </div>

                <div class="agent-navigation">
                    <button class="nav-btn active" data-view="dashboard">📊 Dashboard</button>
                    <button class="nav-btn" data-view="errors">🚨 Eventos de Erro</button>
                    <button class="nav-btn" data-view="patterns">🎯 Padrões de Erro</button>
                    <button class="nav-btn" data-view="logs">📝 Logs do Agente</button>
                    <button class="nav-btn" data-view="config">⚙️ Configuração</button>
                </div>

                <div class="agent-content">
                    <div id="dashboard-view" class="view-content active">
                        ${this.renderDashboard()}
                    </div>
                    <div id="errors-view" class="view-content">
                        ${this.renderErrorsView()}
                    </div>
                    <div id="patterns-view" class="view-content">
                        ${this.renderPatternsView()}
                    </div>
                    <div id="logs-view" class="view-content">
                        ${this.renderLogsView()}
                    </div>
                    <div id="config-view" class="view-content">
                        ${this.renderConfigView()}
                    </div>
                </div>
            </div>
        `;

        // Só executar em modo normal (não em teste)
        if (!this.testMode) {
            this.attachEventListeners();
            this.updateStatus();
            this.loadDashboardData();
        }
    }

    /**
     * Renderiza o dashboard
     */
    renderDashboard() {
        return `
            <div class="dashboard-grid">
                <div class="stats-card">
                    <h3>📈 Estatísticas (24h)</h3>
                    <div id="statistics-content">
                        <div class="loading">Carregando estatísticas...</div>
                    </div>
                </div>
                
                <div class="recent-errors-card">
                    <h3>🚨 Erros Recentes</h3>
                    <div id="recent-errors-content">
                        <div class="loading">Carregando erros recentes...</div>
                    </div>
                </div>
                
                <div class="agent-activity-card">
                    <h3>🤖 Atividade do Agente</h3>
                    <div id="agent-activity-content">
                        <div class="loading">Carregando atividade...</div>
                    </div>
                </div>
                
                <div class="quick-actions-card">
                    <h3>⚡ Ações Rápidas</h3>
                    <div class="quick-actions">
                        <button id="report-error-btn" class="btn btn-primary">
                            📝 Reportar Erro
                        </button>
                        <button id="analyze-error-btn" class="btn btn-info">
                            🔍 Analisar Erro
                        </button>
                        <button id="cleanup-logs-btn" class="btn btn-warning">
                            🧹 Limpar Logs
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Renderiza a view de eventos de erro
     */
    renderErrorsView() {
        return `
            <div class="errors-view">
                <div class="errors-header">
                    <h3>🚨 Eventos de Erro</h3>
                    <div class="errors-filters">
                        <select id="severity-filter">
                            <option value="">Todas as Severidades</option>
                            <option value="2">Warning+</option>
                            <option value="3">Error+</option>
                            <option value="4">Critical</option>
                        </select>
                        <select id="status-filter">
                            <option value="">Todos os Status</option>
                            <option value="0">Detectado</option>
                            <option value="1">Analisando</option>
                            <option value="2">Diagnosticado</option>
                            <option value="3">Corrigindo</option>
                            <option value="4">Corrigido</option>
                            <option value="5">Falhou</option>
                            <option value="6">Escalado</option>
                        </select>
                        <button id="apply-filters-btn" class="btn btn-primary">Aplicar</button>
                    </div>
                </div>
                <div id="errors-list" class="errors-list">
                    <div class="loading">Carregando eventos de erro...</div>
                </div>
            </div>
        `;
    }

    /**
     * Renderiza a view de padrões de erro
     */
    renderPatternsView() {
        return `
            <div class="patterns-view">
                <div class="patterns-header">
                    <h3>🎯 Padrões de Erro</h3>
                    <button id="add-pattern-btn" class="btn btn-success">
                        ➕ Adicionar Padrão
                    </button>
                </div>
                <div id="patterns-list" class="patterns-list">
                    <div class="loading">Carregando padrões de erro...</div>
                </div>
            </div>
        `;
    }

    /**
     * Renderiza a view de logs
     */
    renderLogsView() {
        return `
            <div class="logs-view">
                <div class="logs-header">
                    <h3>📝 Logs do Agente</h3>
                    <div class="logs-filters">
                        <select id="log-level-filter">
                            <option value="">Todos os Níveis</option>
                            <option value="0">Debug+</option>
                            <option value="1">Info+</option>
                            <option value="2">Warning+</option>
                            <option value="3">Error+</option>
                            <option value="4">Critical</option>
                        </select>
                        <input type="text" id="log-source-filter" placeholder="Filtrar por fonte...">
                        <button id="apply-log-filters-btn" class="btn btn-primary">Aplicar</button>
                    </div>
                </div>
                <div id="logs-list" class="logs-list">
                    <div class="loading">Carregando logs...</div>
                </div>
            </div>
        `;
    }

    /**
     * Renderiza a view de configuração
     */
    renderConfigView() {
        return `
            <div class="config-view">
                <div class="config-header">
                    <h3>⚙️ Configuração do Agente</h3>
                    <button id="save-config-btn" class="btn btn-success">
                        💾 Salvar Configuração
                    </button>
                </div>
                <div id="config-form" class="config-form">
                    <div class="loading">Carregando configuração...</div>
                </div>
            </div>
        `;
    }

    /**
     * Anexa event listeners
     */
    attachEventListeners() {
        // Controles principais
        document.getElementById('start-monitoring-btn')?.addEventListener('click', () => this.startMonitoring());
        document.getElementById('stop-monitoring-btn')?.addEventListener('click', () => this.stopMonitoring());
        document.getElementById('refresh-btn')?.addEventListener('click', () => this.refresh());
        document.getElementById('health-check-btn')?.addEventListener('click', () => this.runHealthCheck());

        // Navegação
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchView(e.target.dataset.view));
        });

        // Ações rápidas
        document.getElementById('report-error-btn')?.addEventListener('click', () => this.showReportErrorDialog());
        document.getElementById('analyze-error-btn')?.addEventListener('click', () => this.showAnalyzeErrorDialog());
        document.getElementById('cleanup-logs-btn')?.addEventListener('click', () => this.cleanupLogs());

        // Filtros
        document.getElementById('apply-filters-btn')?.addEventListener('click', () => this.applyErrorFilters());
        document.getElementById('apply-log-filters-btn')?.addEventListener('click', () => this.applyLogFilters());

        // Padrões
        document.getElementById('add-pattern-btn')?.addEventListener('click', () => this.showAddPatternDialog());

        // Configuração
        document.getElementById('save-config-btn')?.addEventListener('click', () => this.saveConfiguration());
    }

    /**
     * Atualiza o status do agente
     */
    async updateStatus() {
        try {
            const status = await this.service.checkAgentStatus();
            this.handleStatusUpdate(status);
        } catch (error) {
            console.error('Erro ao atualizar status:', error);
        }
    }

    /**
     * Manipula atualização de status
     */
    handleStatusUpdate(status) {
        const indicator = document.getElementById('status-indicator');
        const text = document.getElementById('status-text');
        const startBtn = document.getElementById('start-monitoring-btn');
        const stopBtn = document.getElementById('stop-monitoring-btn');

        if (indicator && text) {
            if (status.IsMonitoring) {
                indicator.className = 'status-indicator active';
                text.textContent = 'Monitoramento Ativo';
                if (startBtn) startBtn.style.display = 'none';
                if (stopBtn) stopBtn.style.display = 'inline-block';
            } else {
                indicator.className = 'status-indicator inactive';
                text.textContent = 'Monitoramento Inativo';
                if (startBtn) startBtn.style.display = 'inline-block';
                if (stopBtn) stopBtn.style.display = 'none';
            }
        }
    }

    /**
     * Manipula novo erro
     */
    handleNewError(errorEvent) {
        this.showNotification(`Novo erro detectado: ${errorEvent.Message}`, 'warning');
        if (this.currentView === 'dashboard' || this.currentView === 'errors') {
            this.refresh();
        }
    }

    /**
     * Manipula erro corrigido
     */
    handleErrorFixed(errorEventId, result) {
        this.showNotification('Erro corrigido automaticamente!', 'success');
        if (this.currentView === 'dashboard' || this.currentView === 'errors') {
            this.refresh();
        }
    }

    /**
     * Manipula atualização de logs
     */
    handleLogUpdate(logs) {
        if (this.currentView === 'logs') {
            this.renderLogsList(logs);
        }
    }

    /**
     * Inicia monitoramento
     */
    async startMonitoring() {
        try {
            await this.service.startMonitoring();
            this.showNotification('Monitoramento iniciado com sucesso!', 'success');
        } catch (error) {
            this.showError('Erro ao iniciar monitoramento: ' + error.message);
        }
    }

    /**
     * Para monitoramento
     */
    async stopMonitoring() {
        try {
            await this.service.stopMonitoring();
            this.showNotification('Monitoramento parado com sucesso!', 'info');
        } catch (error) {
            this.showError('Erro ao parar monitoramento: ' + error.message);
        }
    }

    /**
     * Atualiza dados
     */
    async refresh() {
        try {
            await this.updateStatus();
            
            switch (this.currentView) {
                case 'dashboard':
                    await this.loadDashboardData();
                    break;
                case 'errors':
                    await this.loadErrorEvents();
                    break;
                case 'patterns':
                    await this.loadErrorPatterns();
                    break;
                case 'logs':
                    await this.loadAgentLogs();
                    break;
                case 'config':
                    await this.loadConfiguration();
                    break;
            }
            
            this.showNotification('Dados atualizados!', 'success');
        } catch (error) {
            this.showError('Erro ao atualizar dados: ' + error.message);
        }
    }

    /**
     * Executa verificação de saúde
     */
    async runHealthCheck() {
        try {
            this.showNotification('Executando verificação de saúde...', 'info');
            const result = await this.service.runHealthCheck();
            
            if (result.success) {
                this.showNotification('Sistema saudável!', 'success');
            } else {
                this.showNotification('Problemas detectados no sistema', 'warning');
            }
        } catch (error) {
            this.showError('Erro ao executar verificação de saúde: ' + error.message);
        }
    }

    /**
     * Troca de view
     */
    switchView(viewName) {
        // Atualizar navegação
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${viewName}"]`)?.classList.add('active');

        // Atualizar conteúdo
        document.querySelectorAll('.view-content').forEach(view => {
            view.classList.remove('active');
        });
        document.getElementById(`${viewName}-view`)?.classList.add('active');

        this.currentView = viewName;
        
        // Carregar dados da view
        this.loadViewData(viewName);
    }

    /**
     * Carrega dados da view atual
     */
    async loadViewData(viewName) {
        switch (viewName) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'errors':
                await this.loadErrorEvents();
                break;
            case 'patterns':
                await this.loadErrorPatterns();
                break;
            case 'logs':
                await this.loadAgentLogs();
                break;
            case 'config':
                await this.loadConfiguration();
                break;
        }
    }

    /**
     * Carrega dados do dashboard
     */
    async loadDashboardData() {
        try {
            // Carregar estatísticas
            const statistics = await this.service.loadStatistics();
            this.renderStatistics(statistics);

            // Carregar erros recentes
            const recentErrors = await this.service.loadErrorEvents(0, 5);
            this.renderRecentErrors(recentErrors);

            // Carregar logs recentes do agente
            const recentLogs = await this.service.loadAgentLogs(0, 10);
            this.renderAgentActivity(recentLogs);
        } catch (error) {
            console.error('Erro ao carregar dados do dashboard:', error);
        }
    }

    /**
     * Inicia auto-refresh
     */
    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        if (this.autoRefresh) {
            this.refreshInterval = setInterval(() => {
                this.refresh();
            }, 30000); // 30 segundos
        }
    }

    /**
     * Para auto-refresh
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * Mostra notificação
     */
    showNotification(message, type = 'info') {
        // Implementar sistema de notificação
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // Usar sistema de notificação global se disponível
        if (window.showNotification) {
            window.showNotification(message, type);
        }
    }

    /**
     * Mostra erro
     */
    showError(message) {
        this.showNotification(message, 'error');
    }

    /**
     * Renderiza estatísticas
     */
    renderStatistics(statistics) {
        const container = document.getElementById('statistics-content');
        if (!container || !statistics) return;

        container.innerHTML = `
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">${statistics.TotalErrorsDetected}</div>
                    <div class="stat-label">Erros Detectados</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${statistics.ErrorsFixed}</div>
                    <div class="stat-label">Erros Corrigidos</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${statistics.SuccessRate.toFixed(1)}%</div>
                    <div class="stat-label">Taxa de Sucesso</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${this.formatDuration(statistics.AverageResolutionTime)}</div>
                    <div class="stat-label">Tempo Médio</div>
                </div>
            </div>
        `;
    }

    /**
     * Renderiza erros recentes
     */
    renderRecentErrors(errors) {
        const container = document.getElementById('recent-errors-content');
        if (!container) return;

        if (!errors || errors.length === 0) {
            container.innerHTML = '<div class="no-data">Nenhum erro recente</div>';
            return;
        }

        container.innerHTML = `
            <div class="recent-errors-list">
                ${errors.map(error => `
                    <div class="error-item" data-error-id="${error.Id}">
                        <div class="error-header">
                            <span class="error-severity ${this.service.formatSeverity(error.Severity).class}">
                                ${this.service.formatSeverity(error.Severity).icon}
                                ${this.service.formatSeverity(error.Severity).text}
                            </span>
                            <span class="error-status ${this.service.formatErrorStatus(error.Status).class}">
                                ${this.service.formatErrorStatus(error.Status).icon}
                                ${this.service.formatErrorStatus(error.Status).text}
                            </span>
                        </div>
                        <div class="error-message">${error.Message}</div>
                        <div class="error-meta">
                            <span class="error-source">📍 ${error.Source}</span>
                            <span class="error-time">🕒 ${this.service.formatTimestamp(error.Timestamp)}</span>
                        </div>
                        ${error.Status === 0 || error.Status === 2 ? `
                            <button class="btn btn-sm btn-primary fix-error-btn" data-error-id="${error.Id}">
                                🔧 Corrigir
                            </button>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
        `;

        // Anexar event listeners para botões de correção
        container.querySelectorAll('.fix-error-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const errorId = e.target.dataset.errorId;
                this.executeAutoFix(errorId);
            });
        });
    }

    /**
     * Renderiza atividade do agente
     */
    renderAgentActivity(logs) {
        const container = document.getElementById('agent-activity-content');
        if (!container) return;

        if (!logs || logs.length === 0) {
            container.innerHTML = '<div class="no-data">Nenhuma atividade recente</div>';
            return;
        }

        container.innerHTML = `
            <div class="activity-list">
                ${logs.map(log => `
                    <div class="activity-item">
                        <div class="activity-header">
                            <span class="activity-level ${this.service.formatSeverity(log.Level).class}">
                                ${this.service.formatSeverity(log.Level).icon}
                            </span>
                            <span class="activity-source">${log.Source}</span>
                            <span class="activity-time">${this.service.formatTimestamp(log.Timestamp)}</span>
                        </div>
                        <div class="activity-message">${log.Message}</div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Carrega eventos de erro
     */
    async loadErrorEvents() {
        try {
            const severityFilter = document.getElementById('severity-filter')?.value || null;
            const statusFilter = document.getElementById('status-filter')?.value || null;

            const errors = await this.service.loadErrorEvents(0, 50, severityFilter, statusFilter);
            this.renderErrorsList(errors);
        } catch (error) {
            console.error('Erro ao carregar eventos de erro:', error);
        }
    }

    /**
     * Renderiza lista de erros
     */
    renderErrorsList(errors) {
        const container = document.getElementById('errors-list');
        if (!container) return;

        if (!errors || errors.length === 0) {
            container.innerHTML = '<div class="no-data">Nenhum evento de erro encontrado</div>';
            return;
        }

        container.innerHTML = `
            <div class="errors-table">
                ${errors.map(error => `
                    <div class="error-row" data-error-id="${error.Id}">
                        <div class="error-main">
                            <div class="error-info">
                                <div class="error-badges">
                                    <span class="badge severity-${this.service.formatSeverity(error.Severity).class}">
                                        ${this.service.formatSeverity(error.Severity).icon}
                                        ${this.service.formatSeverity(error.Severity).text}
                                    </span>
                                    <span class="badge status-${this.service.formatErrorStatus(error.Status).class}">
                                        ${this.service.formatErrorStatus(error.Status).icon}
                                        ${this.service.formatErrorStatus(error.Status).text}
                                    </span>
                                </div>
                                <div class="error-message">${error.Message}</div>
                                <div class="error-details">
                                    <span>📍 ${error.Source}</span>
                                    <span>🕒 ${this.service.formatTimestamp(error.Timestamp)}</span>
                                    ${error.ResolvedAt ? `<span>✅ Resolvido em ${this.service.formatTimestamp(error.ResolvedAt)}</span>` : ''}
                                </div>
                            </div>
                            <div class="error-actions">
                                <button class="btn btn-sm btn-info view-error-btn" data-error-id="${error.Id}">
                                    👁️ Ver Detalhes
                                </button>
                                ${(error.Status === 0 || error.Status === 2) ? `
                                    <button class="btn btn-sm btn-primary fix-error-btn" data-error-id="${error.Id}">
                                        🔧 Corrigir
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                        ${error.FixAttempts && error.FixAttempts.length > 0 ? `
                            <div class="fix-attempts">
                                <h5>Tentativas de Correção:</h5>
                                ${error.FixAttempts.map(attempt => `
                                    <div class="fix-attempt ${attempt.Success ? 'success' : 'failed'}">
                                        <span class="attempt-icon">${attempt.Success ? '✅' : '❌'}</span>
                                        <span class="attempt-description">${attempt.Description}</span>
                                        <span class="attempt-time">${this.service.formatTimestamp(attempt.Timestamp)}</span>
                                        ${attempt.ErrorMessage ? `<div class="attempt-error">${attempt.ErrorMessage}</div>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
        `;

        // Anexar event listeners
        container.querySelectorAll('.fix-error-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const errorId = e.target.dataset.errorId;
                this.executeAutoFix(errorId);
            });
        });

        container.querySelectorAll('.view-error-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const errorId = e.target.dataset.errorId;
                this.showErrorDetails(errorId);
            });
        });
    }

    /**
     * Executa correção automática
     */
    async executeAutoFix(errorId) {
        try {
            this.showNotification('Executando correção automática...', 'info');
            const result = await this.service.executeAutoFix(errorId);

            if (result.message && result.message.includes('sucesso')) {
                this.showNotification('Correção aplicada com sucesso!', 'success');
            } else {
                this.showNotification('Falha na correção automática', 'warning');
            }

            // Atualizar lista
            await this.loadErrorEvents();
        } catch (error) {
            this.showError('Erro ao executar correção: ' + error.message);
        }
    }

    /**
     * Mostra detalhes do erro
     */
    async showErrorDetails(errorId) {
        try {
            // Encontrar erro na lista local
            const error = this.service.errorEvents.find(e => e.Id === errorId);
            if (!error) return;

            // Criar modal com detalhes
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content error-details-modal">
                    <div class="modal-header">
                        <h3>🔍 Detalhes do Erro</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="error-detail-section">
                            <h4>Informações Básicas</h4>
                            <div class="detail-grid">
                                <div><strong>ID:</strong> ${error.Id}</div>
                                <div><strong>Severidade:</strong> ${this.service.formatSeverity(error.Severity).text}</div>
                                <div><strong>Status:</strong> ${this.service.formatErrorStatus(error.Status).text}</div>
                                <div><strong>Fonte:</strong> ${error.Source}</div>
                                <div><strong>Tipo:</strong> ${error.ErrorType || 'N/A'}</div>
                                <div><strong>Timestamp:</strong> ${this.service.formatTimestamp(error.Timestamp)}</div>
                            </div>
                        </div>

                        <div class="error-detail-section">
                            <h4>Mensagem</h4>
                            <div class="error-message-detail">${error.Message}</div>
                        </div>

                        ${error.StackTrace ? `
                            <div class="error-detail-section">
                                <h4>Stack Trace</h4>
                                <pre class="stack-trace">${error.StackTrace}</pre>
                            </div>
                        ` : ''}

                        ${Object.keys(error.Context || {}).length > 0 ? `
                            <div class="error-detail-section">
                                <h4>Contexto</h4>
                                <pre class="context-data">${JSON.stringify(error.Context, null, 2)}</pre>
                            </div>
                        ` : ''}

                        ${error.Tags && error.Tags.length > 0 ? `
                            <div class="error-detail-section">
                                <h4>Tags</h4>
                                <div class="tags">
                                    ${error.Tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        ${(error.Status === 0 || error.Status === 2) ? `
                            <button class="btn btn-primary" onclick="intelligentAgentComponent.executeAutoFix('${error.Id}'); this.closest('.modal-overlay').remove();">
                                🔧 Executar Correção
                            </button>
                        ` : ''}
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove();">
                            Fechar
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Event listener para fechar modal
            modal.querySelector('.modal-close').addEventListener('click', () => {
                modal.remove();
            });

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        } catch (error) {
            this.showError('Erro ao mostrar detalhes: ' + error.message);
        }
    }

    /**
     * Formata duração
     */
    formatDuration(duration) {
        if (!duration) return 'N/A';

        // Assumindo que duration está em formato TimeSpan (hh:mm:ss)
        const parts = duration.split(':');
        if (parts.length >= 3) {
            const hours = parseInt(parts[0]);
            const minutes = parseInt(parts[1]);
            const seconds = parseInt(parts[2]);

            if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else if (minutes > 0) {
                return `${minutes}m ${seconds}s`;
            } else {
                return `${seconds}s`;
            }
        }

        return duration;
    }

    /**
     * Cleanup ao destruir componente
     */
    destroy() {
        this.stopAutoRefresh();

        // Limpar callbacks
        if (this.service) {
            this.service.onStatusUpdate = null;
            this.service.onNewError = null;
            this.service.onErrorFixed = null;
            this.service.onLogUpdate = null;
        }
    }
}

// Exportar para uso global
window.IntelligentAgentComponent = IntelligentAgentComponent;

// Global instance (nome correto para ModuleManager)
const intelligentAgentComponent = new IntelligentAgentComponent();

// Disponibilizar globalmente com nome correto
window.intelligentAgentComponent = intelligentAgentComponent;
