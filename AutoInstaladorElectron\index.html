<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Instalador</title>
    
    <!-- Styles -->
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="css/modules.css">
    <link rel="stylesheet" href="css/advanced-notifications.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h1>🚀 Auto-Instalador</h1>
            <div class="version-info">
                <small>v1.0.0</small>
            </div>
        </div>
        <nav>
            <!-- Navigation items will be populated by ModuleManager -->
        </nav>
        <div class="sidebar-footer">
            <div class="status-indicators">
                <div class="status-item">
                    <span class="status-dot" id="podman-status-dot"></span>
                    <span>Podman</span>
                </div>
                <div class="status-item">
                    <span class="status-dot" id="api-status-dot"></span>
                    <span>API</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content" id="main-content">
        <!-- Module content will be loaded here dynamically -->
        <div class="loading-screen" id="loading-screen">
            <div class="spinner"></div>
            <p>Carregando sistema...</p>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container" class="notification-container"></div>

    <!-- Notification Settings Panel -->
    <div id="notification-settings-overlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;" onclick="notificationSettingsComponent.hide()">
        <div onclick="event.stopPropagation()" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); max-width: 800px; max-height: 90vh; overflow-y: auto;">
            <!-- Content will be loaded by NotificationSettingsComponent -->
        </div>
    </div>

    <!-- SignalR Client -->
    <script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.min.js"></script>

    <!-- Script de limpeza de cache -->
    <script src="clear-cache.js"></script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Notification Manager -->
    <script src="js/NotificationManager.js"></script>
    <script src="js/AdvancedNotificationManager.js"></script>
    <script src="modules/notifications/NotificationSettingsComponent.js"></script>

    <!-- Electron Configuration -->
    <script src="electron-config.js"></script>

    <!-- Compatibility Tester (only in development) -->
    <script src="test-compatibility.js"></script>

    <!-- Frontend Module Manager -->
    <script src="js/ModuleManager.js"></script>

    <!-- Component Testing Tools -->
    <script src="test-components.js"></script>

    <!-- Critical Errors Fix (must load first) -->
    <script src="fix-critical-errors.js"></script>
    <script src="comprehensive-sidebar-fix.js"></script>

    <!-- Sidebar Menu Testing and Fixing Scripts -->
    <script src="test-sidebar-menu.js"></script>
    <script src="fix-sidebar-menu.js"></script>
    <script src="run-sidebar-tests.js"></script>
    <script src="validate-sidebar-complete.js"></script>
    <script src="demo-sidebar-solution.js"></script>
    <script src="test-fixed-sidebar.js"></script>

    <!-- Auto Navigation Cleaner -->
    <script src="clear-auto-navigation.js"></script>

    <!-- Initialize System -->
    <script>
        // NotificationManager is loaded from js/NotificationManager.js
        // showNotification function is available globally

        // Global module navigation function
        function showModule(moduleId) {
            if (window.moduleManager) {
                window.moduleManager.showModule(moduleId);
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                console.log('🚀 Inicializando Auto-Instalador...');

                // Configurar compatibilidade Electron
                if (window.ElectronConfig) {
                    // Configurar tratamento de erros
                    window.ElectronConfig.INIT_FUNCTIONS.setupErrorHandling();

                    // Configurar polyfills
                    window.ElectronConfig.INIT_FUNCTIONS.setupPolyfills();

                    // Verificar dependências
                    const depCheck = window.ElectronConfig.COMPATIBILITY_UTILS.checkAllDependencies();
                    if (!depCheck.allLoaded) {
                        console.warn('⚠️ Algumas dependências não estão carregadas:', depCheck.checks);
                    } else {
                        console.log('✅ Todas as dependências carregadas com sucesso');
                    }
                }

                // Log de informações do ambiente
                if (window.api && window.api.isElectron) {
                    console.log('🖥️ Executando no Electron');
                    console.log('📱 Plataforma:', window.api.platform);
                }

                // Hide loading screen after modules are loaded
                setTimeout(() => {
                    const loadingScreen = document.getElementById('loading-screen');
                    if (loadingScreen) {
                        loadingScreen.style.display = 'none';
                    }
                }, 2000);

                console.log('✅ Auto-Instalador inicializado');

            } catch (error) {
                console.error('❌ Erro ao inicializar:', error);
                showNotification('Erro ao inicializar sistema', 'error');
            }
        });
    </script>
</body>
</html>
