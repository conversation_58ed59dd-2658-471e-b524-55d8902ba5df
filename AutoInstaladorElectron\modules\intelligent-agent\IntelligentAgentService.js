/**
 * Serviço do Agente Inteligente para o frontend Electron
 * Gerencia comunicação com o backend e interface do usuário
 */
class IntelligentAgentService {
    constructor() {
        this.apiUrl = 'http://localhost:5000/api/intelligent-agent';
        this.isMonitoring = false;
        this.eventSource = null;
        this.errorEvents = [];
        this.agentLogs = [];
        this.statistics = null;
        this.configuration = null;
        this.errorPatterns = [];

        // Modo offline para testes (quando backend não está disponível)
        this.offlineMode = false;

        // Callbacks para eventos
        this.onStatusUpdate = null;
        this.onNewError = null;
        this.onErrorFixed = null;
        this.onLogUpdate = null;
    }

    /**
     * Inicializa o serviço
     */
    async initialize() {
        try {
            console.log('Inicializando serviço do Agente Inteligente...');

            // Tentar carregar configuração inicial
            try {
                await this.loadConfiguration();
                await this.loadStatistics();
                await this.loadErrorPatterns();
                await this.checkAgentStatus();

                console.log('✅ Serviço do Agente Inteligente inicializado com backend');
            } catch (backendError) {
                console.warn('⚠️ Backend não disponível, iniciando em modo offline');
                this.offlineMode = true;
                this.initializeOfflineMode();
            }

            return true;
        } catch (error) {
            console.error('Erro ao inicializar serviço do Agente Inteligente:', error);
            // Mesmo com erro, tentar modo offline
            this.offlineMode = true;
            this.initializeOfflineMode();
            return true; // Retornar true para permitir que o componente continue
        }
    }

    /**
     * Inicializa dados fictícios para modo offline
     */
    initializeOfflineMode() {
        console.log('🔧 Inicializando modo offline com dados fictícios...');

        // Configuração padrão
        this.configuration = {
            MonitoringEnabled: false,
            CheckIntervalMinutes: 5,
            MaxRetryAttempts: 3,
            NotificationSettings: {
                EmailEnabled: false,
                WebhookEnabled: false
            }
        };

        // Estatísticas fictícias
        this.statistics = {
            TotalErrorsDetected: 0,
            ErrorsFixed: 0,
            SuccessRate: 0,
            AverageResolutionTime: '00:00:00'
        };

        // Status inicial
        this.isMonitoring = false;

        console.log('✅ Modo offline inicializado com sucesso');
    }

    /**
     * Verifica status atual do agente
     */
    async checkAgentStatus() {
        try {
            const response = await fetch(`${this.apiUrl}/status`);
            if (!response.ok) throw new Error('Falha ao obter status do agente');
            
            const status = await response.json();
            this.isMonitoring = status.IsMonitoring;
            
            if (this.onStatusUpdate) {
                this.onStatusUpdate(status);
            }
            
            return status;
        } catch (error) {
            console.error('Erro ao verificar status do agente:', error);
            throw error;
        }
    }

    /**
     * Inicia monitoramento automático
     */
    async startMonitoring() {
        try {
            const response = await fetch(`${this.apiUrl}/monitoring/start`, {
                method: 'POST'
            });
            
            if (!response.ok) throw new Error('Falha ao iniciar monitoramento');
            
            this.isMonitoring = true;
            await this.checkAgentStatus();
            
            return true;
        } catch (error) {
            console.error('Erro ao iniciar monitoramento:', error);
            throw error;
        }
    }

    /**
     * Para monitoramento automático
     */
    async stopMonitoring() {
        try {
            const response = await fetch(`${this.apiUrl}/monitoring/stop`, {
                method: 'POST'
            });
            
            if (!response.ok) throw new Error('Falha ao parar monitoramento');
            
            this.isMonitoring = false;
            await this.checkAgentStatus();
            
            return true;
        } catch (error) {
            console.error('Erro ao parar monitoramento:', error);
            throw error;
        }
    }

    /**
     * Reporta um erro manualmente
     */
    async reportError(source, message, stackTrace = null, context = {}, severity = 'Error') {
        try {
            const response = await fetch(`${this.apiUrl}/errors/report`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    source,
                    message,
                    stackTrace,
                    context,
                    severity
                })
            });
            
            if (!response.ok) throw new Error('Falha ao reportar erro');
            
            const errorEvent = await response.json();
            this.errorEvents.unshift(errorEvent);
            
            if (this.onNewError) {
                this.onNewError(errorEvent);
            }
            
            return errorEvent;
        } catch (error) {
            console.error('Erro ao reportar erro:', error);
            throw error;
        }
    }

    /**
     * Analisa um erro
     */
    async analyzeError(errorMessage, stackTrace = null, source = null, context = {}) {
        try {
            const response = await fetch(`${this.apiUrl}/errors/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    errorMessage,
                    stackTrace,
                    source,
                    context
                })
            });
            
            if (!response.ok) throw new Error('Falha ao analisar erro');
            
            return await response.json();
        } catch (error) {
            console.error('Erro ao analisar erro:', error);
            throw error;
        }
    }

    /**
     * Executa correção automática
     */
    async executeAutoFix(errorEventId, fixId = null) {
        try {
            const body = fixId ? JSON.stringify({ fixId }) : null;
            
            const response = await fetch(`${this.apiUrl}/errors/${errorEventId}/fix`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body
            });
            
            if (!response.ok) throw new Error('Falha ao executar correção');
            
            const result = await response.json();
            
            // Atualizar evento na lista local
            const eventIndex = this.errorEvents.findIndex(e => e.Id === errorEventId);
            if (eventIndex !== -1) {
                await this.loadErrorEvents(); // Recarregar para obter status atualizado
            }
            
            if (this.onErrorFixed) {
                this.onErrorFixed(errorEventId, result);
            }
            
            return result;
        } catch (error) {
            console.error('Erro ao executar correção:', error);
            throw error;
        }
    }

    /**
     * Carrega eventos de erro
     */
    async loadErrorEvents(skip = 0, take = 50, minSeverity = null, status = null) {
        try {
            let url = `${this.apiUrl}/errors?skip=${skip}&take=${take}`;
            if (minSeverity) url += `&minSeverity=${minSeverity}`;
            if (status) url += `&status=${status}`;
            
            const response = await fetch(url);
            if (!response.ok) throw new Error('Falha ao carregar eventos de erro');
            
            this.errorEvents = await response.json();
            return this.errorEvents;
        } catch (error) {
            console.error('Erro ao carregar eventos de erro:', error);
            throw error;
        }
    }

    /**
     * Carrega padrões de erro
     */
    async loadErrorPatterns() {
        try {
            const response = await fetch(`${this.apiUrl}/patterns`);
            if (!response.ok) throw new Error('Falha ao carregar padrões de erro');
            
            this.errorPatterns = await response.json();
            return this.errorPatterns;
        } catch (error) {
            console.error('Erro ao carregar padrões de erro:', error);
            throw error;
        }
    }

    /**
     * Salva padrão de erro
     */
    async saveErrorPattern(pattern) {
        try {
            const response = await fetch(`${this.apiUrl}/patterns`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(pattern)
            });
            
            if (!response.ok) throw new Error('Falha ao salvar padrão de erro');
            
            const savedPattern = await response.json();
            
            // Atualizar lista local
            const existingIndex = this.errorPatterns.findIndex(p => p.Id === savedPattern.Id);
            if (existingIndex !== -1) {
                this.errorPatterns[existingIndex] = savedPattern;
            } else {
                this.errorPatterns.push(savedPattern);
            }
            
            return savedPattern;
        } catch (error) {
            console.error('Erro ao salvar padrão de erro:', error);
            throw error;
        }
    }

    /**
     * Remove padrão de erro
     */
    async deleteErrorPattern(patternId) {
        try {
            const response = await fetch(`${this.apiUrl}/patterns/${patternId}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) throw new Error('Falha ao remover padrão de erro');
            
            // Remover da lista local
            this.errorPatterns = this.errorPatterns.filter(p => p.Id !== patternId);
            
            return true;
        } catch (error) {
            console.error('Erro ao remover padrão de erro:', error);
            throw error;
        }
    }

    /**
     * Carrega logs do agente
     */
    async loadAgentLogs(skip = 0, take = 100, minLevel = null, source = null) {
        try {
            let url = `${this.apiUrl}/logs?skip=${skip}&take=${take}`;
            if (minLevel) url += `&minLevel=${minLevel}`;
            if (source) url += `&source=${encodeURIComponent(source)}`;
            
            const response = await fetch(url);
            if (!response.ok) throw new Error('Falha ao carregar logs do agente');
            
            this.agentLogs = await response.json();
            
            if (this.onLogUpdate) {
                this.onLogUpdate(this.agentLogs);
            }
            
            return this.agentLogs;
        } catch (error) {
            console.error('Erro ao carregar logs do agente:', error);
            throw error;
        }
    }

    /**
     * Carrega estatísticas
     */
    async loadStatistics(periodHours = 24) {
        try {
            const response = await fetch(`${this.apiUrl}/statistics?periodHours=${periodHours}`);
            if (!response.ok) throw new Error('Falha ao carregar estatísticas');
            
            this.statistics = await response.json();
            return this.statistics;
        } catch (error) {
            console.error('Erro ao carregar estatísticas:', error);
            throw error;
        }
    }

    /**
     * Carrega configuração
     */
    async loadConfiguration() {
        try {
            const response = await fetch(`${this.apiUrl}/configuration`);
            if (!response.ok) throw new Error('Falha ao carregar configuração');
            
            this.configuration = await response.json();
            return this.configuration;
        } catch (error) {
            console.error('Erro ao carregar configuração:', error);
            throw error;
        }
    }

    /**
     * Atualiza configuração
     */
    async updateConfiguration(configuration) {
        try {
            const response = await fetch(`${this.apiUrl}/configuration`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(configuration)
            });
            
            if (!response.ok) throw new Error('Falha ao atualizar configuração');
            
            this.configuration = await response.json();
            return this.configuration;
        } catch (error) {
            console.error('Erro ao atualizar configuração:', error);
            throw error;
        }
    }

    /**
     * Executa verificação de saúde
     */
    async runHealthCheck() {
        try {
            const response = await fetch(`${this.apiUrl}/health-check`, {
                method: 'POST'
            });
            
            if (!response.ok) throw new Error('Falha ao executar verificação de saúde');
            
            return await response.json();
        } catch (error) {
            console.error('Erro ao executar verificação de saúde:', error);
            throw error;
        }
    }

    /**
     * Executa limpeza de logs
     */
    async cleanupLogs() {
        try {
            const response = await fetch(`${this.apiUrl}/cleanup`, {
                method: 'POST'
            });
            
            if (!response.ok) throw new Error('Falha ao executar limpeza');
            
            return await response.json();
        } catch (error) {
            console.error('Erro ao executar limpeza:', error);
            throw error;
        }
    }

    /**
     * Formata severidade para exibição
     */
    formatSeverity(severity) {
        const severityMap = {
            0: { text: 'Debug', class: 'debug', icon: '🔍' },
            1: { text: 'Info', class: 'info', icon: 'ℹ️' },
            2: { text: 'Warning', class: 'warning', icon: '⚠️' },
            3: { text: 'Error', class: 'error', icon: '❌' },
            4: { text: 'Critical', class: 'critical', icon: '🚨' }
        };
        
        return severityMap[severity] || severityMap[3];
    }

    /**
     * Formata status de erro para exibição
     */
    formatErrorStatus(status) {
        const statusMap = {
            0: { text: 'Detectado', class: 'detected', icon: '🔍' },
            1: { text: 'Analisando', class: 'analyzing', icon: '🔄' },
            2: { text: 'Diagnosticado', class: 'diagnosed', icon: '🎯' },
            3: { text: 'Corrigindo', class: 'fixing', icon: '🔧' },
            4: { text: 'Corrigido', class: 'fixed', icon: '✅' },
            5: { text: 'Falhou', class: 'failed', icon: '❌' },
            6: { text: 'Escalado', class: 'escalated', icon: '⬆️' }
        };
        
        return statusMap[status] || statusMap[0];
    }

    /**
     * Formata timestamp para exibição
     */
    formatTimestamp(timestamp) {
        return new Date(timestamp).toLocaleString('pt-BR');
    }
}

// Exportar para uso global
window.IntelligentAgentService = IntelligentAgentService;
