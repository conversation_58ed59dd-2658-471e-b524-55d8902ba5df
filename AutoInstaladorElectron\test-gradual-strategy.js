// Script de diagnóstico para estratégia de habilitação gradual

console.log('🎯 DIAGNÓSTICO DA ESTRATÉGIA DE HABILITAÇÃO GRADUAL');
console.log('================================================\n');

// Componentes que DEVEM estar habilitados
const enabledComponents = {
    // Categoria Containers (4 componentes funcionais)
    'dashboardComponent': 'Dashboard',
    'applicationsComponent': 'Aplicações',
    'dockerComponent': 'Podman',
    'templatesComponent': 'Templates',
    
    // Categoria Utilitários (1 componente para teste)
    'intelligentAgentComponent': 'Agente Inteligente'
};

// Componentes que devem estar DESABILITADOS temporariamente
const disabledComponents = [
    'startupTimerComponent', // Utilitários - desabilitado temporariamente
    'sslComponent', 'domainsComponent', 'monitoringComponent', // Configurações
    'autoScalingComponent', 'backupComponent', 'githubComponent', // Automação
    'resourceMonitoringComponent', 'aiComponent', 'analyticsComponent', // Monitoramento
    'networkComponent', 'workflowComponent', 'marketplaceComponent', 'testingComponent' // Outros
];

function testGradualStrategy() {
    console.log('📊 TESTANDO ESTRATÉGIA GRADUAL');
    console.log('==============================\n');
    
    let enabledFound = 0;
    let enabledMissing = 0;
    let disabledFound = 0;
    
    console.log('✅ COMPONENTES QUE DEVEM ESTAR HABILITADOS:');
    console.log('==========================================');
    
    Object.keys(enabledComponents).forEach((componentName, index) => {
        const component = window[componentName];
        const displayName = enabledComponents[componentName];
        
        if (component) {
            console.log(`${index + 1}. ✅ ${displayName} (${componentName}) - ENCONTRADO`);
            
            // Verificar se tem método initialize
            if (typeof component.initialize === 'function') {
                console.log(`   ✅ Método initialize() disponível`);
            } else {
                console.log(`   ❌ Método initialize() NÃO encontrado`);
            }
            
            enabledFound++;
        } else {
            console.log(`${index + 1}. ❌ ${displayName} (${componentName}) - NÃO ENCONTRADO`);
            enabledMissing++;
        }
    });
    
    console.log('\n❌ COMPONENTES QUE DEVEM ESTAR DESABILITADOS:');
    console.log('============================================');
    
    disabledComponents.forEach((componentName, index) => {
        const component = window[componentName];
        
        if (component) {
            console.log(`${index + 1}. ⚠️ ${componentName} - ENCONTRADO (deveria estar desabilitado)`);
            disabledFound++;
        } else {
            console.log(`${index + 1}. ✅ ${componentName} - NÃO ENCONTRADO (correto)`);
        }
    });
    
    console.log('\n📊 RESUMO DA ESTRATÉGIA GRADUAL:');
    console.log('===============================');
    console.log(`✅ Componentes habilitados encontrados: ${enabledFound}/5`);
    console.log(`❌ Componentes habilitados faltando: ${enabledMissing}/5`);
    console.log(`⚠️ Componentes desabilitados ainda presentes: ${disabledFound}/${disabledComponents.length}`);
    
    // Verificar todas as variáveis globais
    const allGlobals = Object.keys(window);
    const componentGlobals = allGlobals.filter(key => key.endsWith('Component'));
    
    console.log(`\n🔍 TOTAL DE COMPONENTES GLOBAIS ENCONTRADOS: ${componentGlobals.length}`);
    console.log('Componentes globais:', componentGlobals);
    
    // Status da estratégia
    console.log('\n🎯 STATUS DA ESTRATÉGIA:');
    console.log('=======================');
    
    if (enabledFound === 5 && enabledMissing === 0) {
        console.log('🎉 SUCESSO! Todos os 5 componentes habilitados estão funcionando');
        console.log('📋 Próximo passo: Testar navegação de cada botão individualmente');
    } else if (enabledFound > 0) {
        console.log(`⚠️ PARCIAL: ${enabledFound} de 5 componentes funcionando`);
        console.log('🔧 Próximo passo: Investigar componentes faltando');
    } else {
        console.log('❌ FALHA: Nenhum componente habilitado funcionando');
        console.log('🔧 Próximo passo: Verificar carregamento de scripts');
    }
    
    return {
        enabledFound,
        enabledMissing,
        disabledFound,
        totalGlobals: componentGlobals.length,
        success: enabledFound === 5 && enabledMissing === 0
    };
}

function testSidebarButtons() {
    console.log('\n🖱️ TESTANDO BOTÕES DA SIDEBAR');
    console.log('=============================');
    
    // Verificar se a sidebar foi criada corretamente
    const sidebar = document.querySelector('.sidebar');
    if (!sidebar) {
        console.log('❌ Sidebar não encontrada no DOM');
        return;
    }
    
    // Contar categorias visíveis
    const categories = sidebar.querySelectorAll('.nav-category');
    console.log(`📂 Categorias encontradas: ${categories.length}`);
    
    categories.forEach((category, index) => {
        const categoryTitle = category.querySelector('.category-title')?.textContent || 'Sem título';
        const buttons = category.querySelectorAll('.nav-item');
        console.log(`${index + 1}. Categoria: "${categoryTitle}" - ${buttons.length} botões`);
        
        buttons.forEach((button, btnIndex) => {
            const buttonText = button.textContent.trim();
            const moduleId = button.getAttribute('data-module');
            console.log(`   ${btnIndex + 1}. Botão: "${buttonText}" (${moduleId})`);
        });
    });
    
    console.log('\n🎯 RESULTADO ESPERADO:');
    console.log('=====================');
    console.log('✅ 2 categorias: "Containers" e "Utilitários"');
    console.log('✅ 5 botões total: 4 em Containers + 1 em Utilitários');
    console.log('✅ Botões: Dashboard, Aplicações, Podman, Templates, Agente Inteligente');
}

function diagnoseLoadingIssues() {
    console.log('\n🔍 DIAGNÓSTICO DE PROBLEMAS DE CARREGAMENTO');
    console.log('==========================================');
    
    // Verificar scripts carregados
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const moduleScripts = scripts.filter(script => script.src.includes('modules/'));
    
    console.log(`📄 Scripts de módulos carregados: ${moduleScripts.length}`);
    
    const expectedScripts = [
        'modules/intelligent-agent/IntelligentAgentComponent.js'
    ];
    
    expectedScripts.forEach(expectedScript => {
        const found = moduleScripts.find(script => script.src.includes(expectedScript));
        if (found) {
            console.log(`✅ Script encontrado: ${expectedScript}`);
        } else {
            console.log(`❌ Script NÃO encontrado: ${expectedScript}`);
        }
    });
    
    // Verificar erros no console
    console.log('\n🚨 VERIFICAR ERROS NO CONSOLE:');
    console.log('=============================');
    console.log('1. Abra as DevTools (F12)');
    console.log('2. Vá para a aba Console');
    console.log('3. Procure por erros em vermelho');
    console.log('4. Especialmente erros de "SyntaxError" ou "Failed to load"');
}

// Disponibilizar funções globalmente
window.testGradualStrategy = testGradualStrategy;
window.testSidebarButtons = testSidebarButtons;
window.diagnoseLoadingIssues = diagnoseLoadingIssues;

console.log('🧪 FUNÇÕES DE DIAGNÓSTICO DISPONÍVEIS:');
console.log('=====================================');
console.log('  - testGradualStrategy()     // Testa os 5 componentes habilitados');
console.log('  - testSidebarButtons()      // Verifica botões da sidebar');
console.log('  - diagnoseLoadingIssues()   // Diagnostica problemas de carregamento');
console.log('\n💡 EXECUTE: testGradualStrategy() para começar o diagnóstico');
