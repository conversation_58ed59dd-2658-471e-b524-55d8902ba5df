// Script para testar carregamento individual de cada componente

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Lista de todos os componentes que devem ser carregados
const componentsToTest = [
    'modules/ssl/SSLComponent.js',
    'modules/domains/DomainsComponent.js',
    'modules/monitoring/MonitoringComponent.js',
    'modules/autoscaling/AutoScalingComponent.js',
    'modules/backup/BackupComponent.js',
    'modules/github/GitHubComponent.js',
    'modules/monitoring/ResourceMonitoringComponent.js',
    'modules/ai/AIComponent.js',
    'modules/analytics/AnalyticsComponent.js',
    'modules/network/NetworkComponent.js',
    'modules/workflows/WorkflowComponent.js',
    'modules/marketplace/MarketplaceComponent.js',
    'modules/testing/TestingComponent.js',
    'modules/notifications/NotificationSettingsComponent.js',
    'modules/intelligent-agent/IntelligentAgentComponent.js',
    'modules/startup-timer/StartupTimerComponent.js'
];

console.log('🔍 TESTE DE CARREGAMENTO INDIVIDUAL DE COMPONENTES');
console.log('================================================\n');

let passedTests = 0;
let failedTests = 0;
const failedComponents = [];

componentsToTest.forEach((componentPath, index) => {
    const fullPath = path.join(__dirname, componentPath);
    const componentName = path.basename(componentPath, '.js');
    
    console.log(`${index + 1}. Testando ${componentName}...`);
    
    try {
        // Verificar se o arquivo existe
        if (!fs.existsSync(fullPath)) {
            console.log(`   ❌ ARQUIVO NÃO ENCONTRADO: ${componentPath}`);
            failedTests++;
            failedComponents.push({
                component: componentName,
                path: componentPath,
                error: 'Arquivo não encontrado'
            });
            return;
        }
        
        // Testar sintaxe do arquivo
        execSync(`node -c "${fullPath}"`, { stdio: 'pipe' });
        
        // Ler conteúdo do arquivo para verificações adicionais
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // Verificar se tem método initialize
        const hasInitialize = content.includes('async initialize()');
        const hasInit = content.includes('async init()');
        
        // Verificar se tem instanciação global
        const hasGlobalInstance = content.includes('= new ') && content.includes('Component()');
        
        // Verificar se há erros de sintaxe específicos
        const hasUnexpectedToken = content.includes('{{') || content.includes('}}');
        
        if (hasUnexpectedToken) {
            console.log(`   ⚠️ POSSÍVEL PROBLEMA DE SINTAXE: tokens inesperados encontrados`);
        }
        
        if (!hasInitialize && hasInit) {
            console.log(`   ⚠️ PROBLEMA: Ainda usa init() em vez de initialize()`);
            failedTests++;
            failedComponents.push({
                component: componentName,
                path: componentPath,
                error: 'Usa init() em vez de initialize()'
            });
            return;
        }
        
        if (!hasGlobalInstance) {
            console.log(`   ⚠️ PROBLEMA: Não tem instanciação global`);
            failedTests++;
            failedComponents.push({
                component: componentName,
                path: componentPath,
                error: 'Sem instanciação global'
            });
            return;
        }
        
        console.log(`   ✅ OK - Sintaxe válida, tem initialize(), tem instanciação global`);
        passedTests++;
        
    } catch (error) {
        console.log(`   ❌ ERRO DE SINTAXE: ${error.message}`);
        failedTests++;
        failedComponents.push({
            component: componentName,
            path: componentPath,
            error: error.message
        });
    }
    
    console.log('');
});

console.log('📊 RESUMO DOS TESTES:');
console.log('====================');
console.log(`✅ Componentes OK: ${passedTests}`);
console.log(`❌ Componentes com problema: ${failedTests}`);
console.log(`📦 Total testado: ${componentsToTest.length}\n`);

if (failedComponents.length > 0) {
    console.log('❌ COMPONENTES COM PROBLEMAS:');
    console.log('============================');
    failedComponents.forEach((comp, i) => {
        console.log(`${i + 1}. ${comp.component}`);
        console.log(`   📁 ${comp.path}`);
        console.log(`   🔴 Erro: ${comp.error}\n`);
    });
    
    console.log('🔧 AÇÕES NECESSÁRIAS:');
    console.log('=====================');
    console.log('1. Corrigir os componentes listados acima');
    console.log('2. Garantir que todos tenham async initialize()');
    console.log('3. Garantir que todos tenham instanciação global');
    console.log('4. Verificar sintaxe de cada arquivo');
} else {
    console.log('🎉 TODOS OS COMPONENTES ESTÃO OK!');
    console.log('================================');
    console.log('✅ Todos têm sintaxe válida');
    console.log('✅ Todos têm async initialize()');
    console.log('✅ Todos têm instanciação global');
    console.log('\n🔍 Se ainda há problemas de carregamento:');
    console.log('1. Verificar se há conflitos de nomes');
    console.log('2. Verificar ordem de carregamento');
    console.log('3. Verificar se há erros durante execução');
}

console.log('\n🎯 PRÓXIMO PASSO:');
console.log('================');
if (failedTests > 0) {
    console.log('Corrigir os componentes com problemas listados acima');
} else {
    console.log('Investigar por que apenas 12 componentes estão sendo carregados globalmente');
    console.log('Execute no console do browser: debugComponentLoading()');
}
