/**
 * Gerenciador de Módulos Frontend
 * Responsável por carregar, inicializar e gerenciar todos os módulos
 */

class ModuleManager {
    constructor() {
        this.modules = new Map();
        this.currentModule = null;
        this.isInitialized = false;
        this.loadingPromises = new Map();
    }

    /**
     * Inicializa o gerenciador de módulos
     */
    async initialize() {
        try {
            console.log('🔄 Inicializando ModuleManager...');
            
            // Registrar módulos disponíveis
            this.registerModules();
            
            // Carregar módulos essenciais
            await this.loadEssentialModules();

            // Carregar todos os outros módulos (scripts apenas, sem inicializar)
            await this.preloadAllModules();
            
            // Configurar navegação
            this.setupNavigation();

            // Aguardar um pouco para os serviços carregarem e atualizar status da sidebar
            setTimeout(async () => {
                await this.updateSidebarStatus();
            }, 1000);

            // Mostrar dashboard por padrão
            await this.showModule('dashboard');
            
            this.isInitialized = true;
            console.log('✅ ModuleManager inicializado');
            
        } catch (error) {
            console.error('❌ Erro ao inicializar ModuleManager:', error);
        }
    }

    /**
     * Registra todos os módulos disponíveis
     */
    registerModules() {
        const moduleConfigs = [
            {
                id: 'dashboard',
                name: 'Dashboard',
                icon: '🏠',
                component: 'dashboardComponent',
                service: null,
                scripts: [
                    'modules/dashboard/DashboardComponent.js'
                ],
                styles: [
                    'modules/dashboard/styles.css'
                ],
                essential: true
            },
            {
                id: 'applications',
                name: 'Aplicações',
                icon: '📱',
                component: 'applicationsComponent',
                service: 'applicationsService',
                scripts: [
                    'modules/applications/ApplicationsService.js',
                    'modules/applications/ApplicationsComponent.js'
                ],
                styles: [
                    'modules/applications/styles.css'
                ],
                essential: true
            },
            {
                id: 'templates',
                name: 'Templates',
                icon: '📋',
                component: 'templatesComponent',
                service: 'templatesService',
                scripts: [
                    'modules/templates/TemplatesService.js',
                    'modules/templates/TemplatesComponent.js'
                ],
                styles: [
                    'modules/dashboard/styles.css'
                ],
                essential: true
            },
            {
                id: 'ports',
                name: 'Portas',
                icon: '🔌',
                component: 'portsComponent',
                service: null,
                scripts: [
                    'modules/ports/PortsComponent.js'
                ],
                styles: [
                    'modules/dashboard/styles.css'
                ],
                essential: false
            },
            {
                id: 'docker',
                name: 'Podman',
                icon: '🐳',
                component: 'dockerComponent',
                service: 'dockerService',
                scripts: [
                    'modules/docker/DockerService.js',
                    'modules/docker/DockerComponent.js'
                ],
                styles: [
                    'modules/dashboard/styles.css'
                ],
                essential: false
            },
            {
                id: 'ssl',
                name: 'SSL',
                icon: '🔒',
                component: 'sslComponent',
                service: null,
                scripts: [
                    'modules/ssl/SSLComponent.js'
                ],
                styles: [
                    'modules/ssl/styles.css'
                ],
                essential: false
            },
            {
                id: 'domains',
                name: 'Domínios',
                icon: '🌐',
                component: 'domainsComponent',
                service: null,
                scripts: [
                    'modules/domains/DomainsComponent.js'
                ],
                styles: [
                    'modules/domains/styles.css'
                ],
                essential: false
            },
            {
                id: 'monitoring',
                name: 'Monitoramento',
                icon: '📊',
                component: 'monitoringComponent',
                service: null,
                scripts: [
                    'modules/monitoring/MonitoringComponent.js'
                ],
                styles: [
                    'modules/monitoring/styles.css'
                ],
                essential: false
            },
            {
                id: 'autoscaling',
                name: 'Auto-scaling',
                icon: '⚡',
                component: 'autoScalingComponent',
                service: null,
                scripts: [
                    'modules/autoscaling/AutoScalingComponent.js'
                ],
                styles: [
                    'modules/autoscaling/styles.css'
                ],
                essential: false
            },
            {
                id: 'backup',
                name: 'Backup',
                icon: '💾',
                component: 'backupComponent',
                service: null,
                scripts: [
                    'modules/backup/BackupComponent.js'
                ],
                styles: [
                    'modules/backup/styles.css'
                ],
                essential: false
            },
            {
                id: 'github',
                name: 'GitHub Actions',
                icon: '🐙',
                component: 'githubComponent',
                service: null,
                scripts: [
                    'modules/github/GitHubComponent.js'
                ],
                styles: [
                    'modules/github/styles.css'
                ],
                essential: false
            },
            {
                id: 'resource-monitoring',
                name: 'Resource Monitoring',
                icon: '📈',
                component: 'resourceMonitoringComponent',
                service: null,
                scripts: [
                    'modules/monitoring/ChartManager.js',
                    'modules/monitoring/ResourceMonitoringComponent.js'
                ],
                styles: [
                    'modules/monitoring/resource-monitoring-styles.css'
                ],
                essential: false
            },
            {
                id: 'ai',
                name: 'Inteligência Artificial',
                icon: '🤖',
                component: 'aiComponent',
                service: null,
                scripts: [
                    'modules/ai/AIComponent.js'
                ],
                styles: [
                    'modules/ai/ai-styles.css'
                ],
                essential: false
            },
            {
                id: 'analytics',
                name: 'Dashboard Analytics',
                icon: '📊',
                component: 'analyticsComponent',
                service: null,
                scripts: [
                    'modules/analytics/AnalyticsComponent.js'
                ],
                styles: [
                    'modules/analytics/analytics-styles.css'
                ],
                essential: false
            },
            {
                id: 'network',
                name: 'Rede e Conectividade',
                icon: '🌐',
                component: 'networkComponent',
                service: null,
                scripts: [
                    'modules/network/NetworkComponent.js'
                ],
                styles: [
                    'modules/network/network-styles.css'
                ],
                essential: false
            },
            {
                id: 'workflows',
                name: 'Workflows/Automação',
                icon: '🔄',
                component: 'workflowComponent',
                service: null,
                scripts: [
                    'modules/workflows/WorkflowComponent.js'
                ],
                styles: [
                    'modules/workflows/workflow-styles.css'
                ],
                essential: false
            },
            {
                id: 'marketplace',
                name: 'Templates Marketplace',
                icon: '🏪',
                component: 'marketplaceComponent',
                service: null,
                scripts: [
                    'modules/marketplace/MarketplaceComponent.js'
                ],
                styles: [
                    'modules/marketplace/marketplace-styles.css'
                ],
                essential: false
            },
            {
                id: 'testing',
                name: 'Testes e Validação',
                icon: '🧪',
                component: 'testingComponent',
                service: null,
                scripts: [
                    'modules/testing/TestingComponent.js'
                ],
                styles: [
                    'modules/testing/testing-styles.css'
                ],
                essential: false
            },
            {
                id: 'notifications',
                name: 'Configurações de Notificações',
                icon: '🔔',
                component: 'notificationSettingsComponent',
                service: null,
                scripts: [
                    'modules/notifications/NotificationSettingsComponent.js'
                ],
                styles: [
                    'modules/notifications/styles.css'
                ],
                essential: false
            },
            {
                id: 'intelligent-agent',
                name: 'Agente Inteligente',
                icon: '🤖',
                component: 'intelligentAgentComponent',
                service: 'intelligentAgentService',
                scripts: [
                    'modules/intelligent-agent/IntelligentAgentService.js',
                    'modules/intelligent-agent/IntelligentAgentComponent.js'
                ],
                styles: [
                    'modules/intelligent-agent/styles.css'
                ],
                essential: false
            },
            {
                id: 'startup-timer',
                name: 'Temporizador de Startup',
                icon: '⏱️',
                component: 'startupTimerComponent',
                service: 'startupTimerService',
                scripts: [
                    'modules/startup-timer/StartupTimerService.js',
                    'modules/startup-timer/StartupTimerComponent.js'
                ],
                styles: [
                    'modules/startup-timer/styles.css'
                ],
                essential: false
            }
        ];

        moduleConfigs.forEach(config => {
            this.modules.set(config.id, {
                ...config,
                loaded: false,
                initialized: false,
                instance: null
            });
        });

        console.log(`📦 ${this.modules.size} módulos registrados`);
    }

    /**
     * Carrega módulos essenciais
     */
    async loadEssentialModules() {
        const essentialModules = Array.from(this.modules.values())
            .filter(module => module.essential);

        console.log(`⚡ Carregando ${essentialModules.length} módulos essenciais...`);

        const loadPromises = essentialModules.map(module => 
            this.loadModule(module.id)
        );

        await Promise.allSettled(loadPromises);
        console.log('✅ Módulos essenciais carregados');
    }

    /**
     * Pré-carrega apenas os módulos habilitados (estratégia gradual)
     */
    async preloadAllModules() {
        // ESTRATÉGIA GRADUAL: Carregar apenas módulos habilitados
        const enabledModuleIds = [
            'dashboard', 'applications', 'docker', 'templates', // Containers (funcionando)
            'intelligent-agent' // Utilitários (teste isolado)
        ];

        const nonEssentialModules = Array.from(this.modules.values())
            .filter(module => !module.essential && !module.loaded && enabledModuleIds.includes(module.id));

        console.log(`📦 ESTRATÉGIA GRADUAL: Pré-carregando apenas ${nonEssentialModules.length} módulos habilitados...`);
        console.log(`📋 Módulos habilitados: ${enabledModuleIds.join(', ')}`);

        for (const module of nonEssentialModules) {
            try {
                console.log(`📄 Pré-carregando scripts do módulo ${module.name}...`);

                // Carregar apenas estilos e scripts, sem inicializar
                if (module.styles) {
                    console.log(`  🎨 Carregando estilos: ${module.styles.join(', ')}`);
                    await this.loadStyles(module.styles);
                }

                if (module.scripts) {
                    console.log(`  📄 Carregando scripts: ${module.scripts.join(', ')}`);
                    await this.loadScripts(module.scripts);

                    // Verificar se o componente foi criado globalmente
                    if (module.component) {
                        setTimeout(() => {
                            if (window[module.component]) {
                                console.log(`  ✅ Componente ${module.component} disponível globalmente`);
                            } else {
                                console.error(`  ❌ Componente ${module.component} NÃO disponível após carregamento`);
                            }
                        }, 100);
                    }
                }

                console.log(`✅ Scripts do módulo ${module.name} pré-carregados`);
            } catch (error) {
                console.error(`❌ Erro ao pré-carregar módulo ${module.name}:`, error);
                console.error(`  Detalhes do erro:`, error.message, error.stack);
            }
        }

        console.log('✅ Todos os módulos pré-carregados');
    }

    /**
     * Carrega um módulo específico
     */
    async loadModule(moduleId) {
        const module = this.modules.get(moduleId);
        if (!module) {
            throw new Error(`Módulo ${moduleId} não encontrado`);
        }

        // Evitar carregamento duplicado
        if (this.loadingPromises.has(moduleId)) {
            return this.loadingPromises.get(moduleId);
        }

        const loadPromise = this._loadModuleInternal(module);
        this.loadingPromises.set(moduleId, loadPromise);

        try {
            await loadPromise;
            return module;
        } finally {
            this.loadingPromises.delete(moduleId);
        }
    }

    /**
     * Carregamento interno do módulo
     */
    async _loadModuleInternal(module) {
        if (module.loaded) {
            return module;
        }

        try {
            console.log(`📦 Carregando módulo ${module.name}...`);

            // Carregar estilos
            if (module.styles) {
                await this.loadStyles(module.styles);
            }

            // Carregar scripts
            if (module.scripts) {
                await this.loadScripts(module.scripts);
            }

            // Inicializar componente
            if (module.component) {
                await this.initializeComponent(module);
            }

            module.loaded = true;
            console.log(`✅ Módulo ${module.name} carregado`);

        } catch (error) {
            console.error(`❌ Erro ao carregar módulo ${module.name}:`, error);
            throw error;
        }

        return module;
    }

    /**
     * Carrega arquivos CSS
     */
    async loadStyles(stylePaths) {
        const loadPromises = stylePaths.map(path => {
            return new Promise((resolve, reject) => {
                // Verificar se já foi carregado
                if (document.querySelector(`link[href="${path}"]`)) {
                    resolve();
                    return;
                }

                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = path;
                link.onload = resolve;
                link.onerror = reject;
                document.head.appendChild(link);
            });
        });

        await Promise.all(loadPromises);
    }

    /**
     * Carrega arquivos JavaScript
     */
    async loadScripts(scriptPaths) {
        // Carregar scripts sequencialmente para manter dependências
        for (const path of scriptPaths) {
            await this.loadScript(path);
        }
    }

    /**
     * Carrega um script individual
     */
    async loadScript(path) {
        return new Promise((resolve, reject) => {
            // Verificar se já foi carregado
            if (document.querySelector(`script[src="${path}"]`)) {
                console.log(`📄 Script já carregado: ${path}`);
                resolve();
                return;
            }

            console.log(`📄 Carregando script: ${path}`);
            const script = document.createElement('script');
            script.src = path;
            script.onload = () => {
                console.log(`✅ Script carregado com sucesso: ${path}`);
                resolve();
            };
            script.onerror = (error) => {
                console.error(`❌ Erro ao carregar script: ${path}`, error);
                console.error(`  Event:`, error);
                console.error(`  Script src:`, script.src);
                console.error(`  Script readyState:`, script.readyState);
                reject(new Error(`Falha ao carregar script: ${path}`));
            };
            document.head.appendChild(script);
        });
    }

    /**
     * Inicializa componente do módulo
     */
    async initializeComponent(module) {
        try {
            // Aguardar componente estar disponível
            await this.waitForGlobal(module.component);

            const componentInstance = window[module.component];
            if (componentInstance) {
                // Tentar método initialize primeiro, depois init
                if (typeof componentInstance.initialize === 'function') {
                    await componentInstance.initialize();
                } else if (typeof componentInstance.init === 'function') {
                    await componentInstance.init();
                }

                module.instance = componentInstance;
                module.initialized = true;
                console.log(`🎯 Componente ${module.component} inicializado`);
            } else {
                console.warn(`⚠️ Componente ${module.component} não encontrado`);
            }
        } catch (error) {
            console.error(`❌ Erro ao inicializar componente ${module.component}:`, error);
        }
    }

    /**
     * Aguarda uma variável global estar disponível
     */
    async waitForGlobal(globalName, timeout = 10000) {
        const startTime = Date.now();

        console.log(`🔍 Aguardando ${globalName} estar disponível...`);

        while (!window[globalName]) {
            if (Date.now() - startTime > timeout) {
                console.error(`❌ Timeout aguardando ${globalName}. Variáveis globais disponíveis:`, Object.keys(window).filter(k => k.includes('Component')));
                throw new Error(`Timeout aguardando ${globalName}`);
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log(`✅ ${globalName} disponível!`);
    }

    /**
     * Mostra um módulo específico
     */
    async showModule(moduleId) {
        try {
            // Carregar módulo se necessário
            const module = await this.loadModule(moduleId);

            // Ocultar módulo atual
            if (this.currentModule && this.currentModule !== moduleId) {
                await this.hideModule(this.currentModule);
            }

            // Mostrar novo módulo
            if (module.instance) {
                if (typeof module.instance.show === 'function') {
                    module.instance.show();
                } else if (typeof module.instance.render === 'function') {
                    // Para novos componentes que usam render()
                    const mainContent = document.getElementById('main-content');
                    if (mainContent) {
                        const html = await module.instance.render();
                        mainContent.innerHTML = html;
                    }
                }
            }

            // Atualizar navegação
            this.updateNavigation(moduleId);

            this.currentModule = moduleId;
            console.log(`👁️ Módulo ${module.name} exibido`);

        } catch (error) {
            console.error(`❌ Erro ao mostrar módulo ${moduleId}:`, error);
        }
    }

    /**
     * Oculta um módulo
     */
    async hideModule(moduleId) {
        const module = this.modules.get(moduleId);
        if (module && module.instance && typeof module.instance.hide === 'function') {
            module.instance.hide();
        }
    }

    /**
     * Configura navegação com categorias
     */
    setupNavigation() {
        const nav = document.querySelector('.sidebar nav');
        if (!nav) return;

        // Limpar navegação existente
        nav.innerHTML = '';

        // ESTRATÉGIA DE HABILITAÇÃO GRADUAL - Apenas 2 categorias habilitadas
        // Definir categorias e seus módulos (SIMPLIFICADO PARA DIAGNÓSTICO)
        const categories = {
            'Containers': ['dashboard', 'applications', 'docker', 'templates'],
            'Utilitários': ['intelligent-agent'] // Apenas Agente Inteligente habilitado inicialmente

            // TEMPORARIAMENTE DESABILITADAS PARA DIAGNÓSTICO:
            // 'Monitoramento': ['monitoring', 'resource-monitoring', 'analytics', 'ai'],
            // 'Configurações': ['backup', 'ssl', 'domains', 'ports', 'network', 'notifications'],
            // 'Automação': ['workflows', 'autoscaling', 'github', 'marketplace', 'testing'],
            // 'Utilitários': ['intelligent-agent', 'startup-timer'] // startup-timer desabilitado temporariamente
        };

        // Criar navegação por categorias
        Object.entries(categories).forEach(([categoryName, moduleIds]) => {
            // Criar header da categoria
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'nav-category';
            categoryHeader.textContent = categoryName;
            nav.appendChild(categoryHeader);

            // Adicionar módulos da categoria
            moduleIds.forEach(moduleId => {
                const module = this.modules.get(moduleId);
                if (!module) return;

                const navItem = document.createElement('a');
                navItem.href = '#';
                navItem.className = 'nav-item';
                navItem.dataset.module = moduleId;
                navItem.innerHTML = `
                    <span class="nav-icon">${module.icon}</span>
                    <span class="nav-text">${module.name}</span>
                `;

                navItem.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.showModule(moduleId);
                });

                nav.appendChild(navItem);
            });
        });
    }

    /**
     * Atualiza estado da navegação
     */
    updateNavigation(activeModuleId) {
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.module === activeModuleId) {
                item.classList.add('active');
            }
        });
    }

    /**
     * Obtém módulo por ID
     */
    getModule(moduleId) {
        return this.modules.get(moduleId);
    }

    /**
     * Obtém todos os módulos
     */
    getAllModules() {
        return Array.from(this.modules.values());
    }

    /**
     * Obtém módulos carregados
     */
    getLoadedModules() {
        return Array.from(this.modules.values()).filter(m => m.loaded);
    }

    /**
     * Verifica se módulo está carregado
     */
    isModuleLoaded(moduleId) {
        const module = this.modules.get(moduleId);
        return module ? module.loaded : false;
    }

    /**
     * Recarrega um módulo
     */
    async reloadModule(moduleId) {
        const module = this.modules.get(moduleId);
        if (!module) return;

        // Resetar estado
        module.loaded = false;
        module.initialized = false;
        module.instance = null;

        // Recarregar
        await this.loadModule(moduleId);
    }

    /**
     * Atualiza status da sidebar
     */
    async updateSidebarStatus() {
        // Podman Status - verificar status real
        const podmanDot = document.getElementById('podman-status-dot');
        if (podmanDot) {
            if (typeof dockerService !== 'undefined') {
                try {
                    const status = await dockerService.getStatusWithCache();
                    if (status && status.isRunning) {
                        podmanDot.className = 'status-dot online';
                    } else if (status && status.isInstalled) {
                        podmanDot.className = 'status-dot warning';
                    } else {
                        podmanDot.className = 'status-dot offline';
                    }
                } catch (error) {
                    console.warn('Erro ao verificar status do Podman na sidebar:', error);
                    podmanDot.className = 'status-dot offline';
                }
            } else {
                podmanDot.className = 'status-dot offline';
            }
        }

        // API Status - sempre online se chegou até aqui
        const apiDot = document.getElementById('api-status-dot');
        if (apiDot) {
            apiDot.className = 'status-dot online';
        }
    }

    /**
     * Obtém estatísticas dos módulos
     */
    getStats() {
        const total = this.modules.size;
        const loaded = this.getLoadedModules().length;
        const initialized = Array.from(this.modules.values()).filter(m => m.initialized).length;

        return {
            total,
            loaded,
            initialized,
            loadedPercentage: Math.round((loaded / total) * 100),
            initializedPercentage: Math.round((initialized / total) * 100)
        };
    }
}

// Exportar instância singleton
const moduleManager = new ModuleManager();

// Para uso no Electron
if (typeof module !== 'undefined' && module.exports) {
    module.exports = moduleManager;
}

// Para uso global
if (typeof window !== 'undefined') {
    window.moduleManager = moduleManager;
    
    // Função global para mostrar módulos
    window.showModule = (moduleId) => moduleManager.showModule(moduleId);
}

// Auto-inicializar quando DOM estiver pronto
if (typeof document !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            moduleManager.initialize();
        });
    } else {
        moduleManager.initialize();
    }
}
