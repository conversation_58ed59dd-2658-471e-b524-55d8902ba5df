/* Estilos para o módulo de Configurações de Notificações */

.notification-settings {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.notification-settings .module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.notification-settings .module-header h2 {
    margin: 0;
    color: #333;
    font-size: 24px;
}

.notification-settings .header-actions {
    display: flex;
    gap: 10px;
}

.notification-settings .settings-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.notification-settings .settings-section h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.notification-settings .form-group {
    margin-bottom: 15px;
}

.notification-settings .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.notification-settings .form-group input,
.notification-settings .form-group select,
.notification-settings .form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.notification-settings .form-group input:focus,
.notification-settings .form-group select:focus,
.notification-settings .form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.notification-settings .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification-settings .checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.notification-settings .checkbox-group input[type="checkbox"] {
    width: auto;
}

.notification-settings .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s;
}

.notification-settings .btn-primary {
    background: #007bff;
    color: white;
}

.notification-settings .btn-primary:hover {
    background: #0056b3;
}

.notification-settings .btn-secondary {
    background: #6c757d;
    color: white;
}

.notification-settings .btn-secondary:hover {
    background: #545b62;
}

.notification-settings .btn-success {
    background: #28a745;
    color: white;
}

.notification-settings .btn-success:hover {
    background: #1e7e34;
}

.notification-settings .btn-danger {
    background: #dc3545;
    color: white;
}

.notification-settings .btn-danger:hover {
    background: #c82333;
}

.notification-settings .webhooks-list {
    margin-top: 20px;
}

.notification-settings .webhook-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 10px;
}

.notification-settings .webhook-item h4 {
    margin: 0 0 10px 0;
    color: #333;
}

.notification-settings .webhook-item .webhook-url {
    font-family: monospace;
    background: #e9ecef;
    padding: 5px 8px;
    border-radius: 3px;
    word-break: break-all;
}

.notification-settings .webhook-actions {
    margin-top: 10px;
    display: flex;
    gap: 10px;
}

.notification-settings .test-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
}

.notification-settings .test-result.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.notification-settings .test-result.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Modal styles */
.notification-settings .modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    align-items: center;
    justify-content: center;
}

.notification-settings .modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.notification-settings .modal-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-settings .modal-header h3 {
    margin: 0;
    color: #333;
}

.notification-settings .modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.notification-settings .modal-close:hover {
    color: #333;
}

.notification-settings .modal-body {
    padding: 20px;
}

.notification-settings .modal-footer {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Responsive */
@media (max-width: 768px) {
    .notification-settings .module-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .notification-settings .header-actions {
        justify-content: center;
    }
    
    .notification-settings .webhook-actions {
        flex-direction: column;
    }
    
    .notification-settings .modal-content {
        width: 95%;
        margin: 10px;
    }
}
